"""
类别标识工具类
用于根据样品分类获取对应的类别标识
"""


class CategoryIdentifierUtil:
    """类别标识工具类"""
    
    # 分类到类别标识的映射
    CLASSIFICATION_IDENTIFIER_MAP = {
        "公共场所": "GG",
        "固": "G",
        "固体废物": "G",
        "土": "T",
        "土壤": "T",
        "声": "S",
        "震动": "Z",
        "气": "Q",
        "水": "S",
        "污泥": "W",
        "海洋": "H",
        "辐射": "F",
        "生态": "ST",
        "能源": "N",
        "水处理剂": "SC",
        "水泥胶砂": "SJ",
        "油气回收": "YH",
    }
    
    @classmethod
    def get_identifier(cls, classification: str) -> str:
        """
        根据分类获取类别标识
        
        :param classification: 分类名称
        :return: 类别标识，如果没有匹配到则返回 'O'
        """
        if not classification:
            return "O"
        
        # 去除首尾空格
        classification = classification.strip()
        
        # 查找映射
        identifier = cls.CLASSIFICATION_IDENTIFIER_MAP.get(classification)
        
        # 如果没有找到，返回默认值 'O'
        return identifier if identifier else "O"
    
    @classmethod
    def get_all_identifiers(cls) -> dict:
        """
        获取所有分类到类别标识的映射
        
        :return: 分类到类别标识的映射字典
        """
        return cls.CLASSIFICATION_IDENTIFIER_MAP.copy()

