# 路由参数自动填充功能修复说明

## 问题描述

项目报价页面和采样任务页面不支持通过URL参数自动填充查询条件，导致从合同履约情况页面跳转时无法实现预期的自动查询功能。

## 修复内容

### 1. 项目报价页面修复

**文件：** `front/src/views/quotation/project-quotation/index.vue`

**修复内容：**
1. 添加 `useRoute` 导入
2. 在 `onMounted` 生命周期中处理路由参数

**修复前：**
```javascript
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(() => {
  getList()
})
```

**修复后：**
```javascript
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

onMounted(() => {
  // 处理路由参数，自动填充查询条件
  if (route.query.projectName) {
    queryParams.value.projectName = route.query.projectName
  }
  if (route.query.projectCode) {
    queryParams.value.projectCode = route.query.projectCode
  }
  if (route.query.customerName) {
    queryParams.value.customerName = route.query.customerName
  }
  
  getList()
})
```

### 2. 采样任务页面修复

**文件：** `front/src/views/sampling/task/index.vue`

**修复内容：**
在 `created` 生命周期中添加路由参数处理逻辑

**修复前：**
```javascript
created() {
  this.getDictData();
  this.getList();
  this.getUserList();
  this.getQuotationList();
},
```

**修复后：**
```javascript
created() {
  // 处理路由参数，自动填充查询条件
  if (this.$route.query.taskName) {
    this.queryParams.taskName = this.$route.query.taskName;
  }
  if (this.$route.query.taskCode) {
    this.queryParams.taskCode = this.$route.query.taskCode;
  }
  if (this.$route.query.status) {
    this.queryParams.status = this.$route.query.status;
  }
  
  this.getDictData();
  this.getList();
  this.getUserList();
  this.getQuotationList();
},
```

### 3. 履约情况跳转功能优化

**文件：** `front/src/views/contract/components/ContractPerformance.vue`

**优化内容：**
添加跳转成功/失败的用户反馈

**优化前：**
```javascript
const goToSamplingTask = (taskName) => {
  router.push({
    path: '/sampling/task',
    query: { taskName: taskName }
  })
}
```

**优化后：**
```javascript
const goToSamplingTask = (taskName) => {
  router.push({
    path: '/sampling/task',
    query: { taskName: taskName }
  }).then(() => {
    ElMessage.success(`已跳转到采样任务页面，查询条件：任务名称="${taskName}"`)
  }).catch(() => {
    ElMessage.error('跳转失败')
  })
}
```

## 支持的路由参数

### 项目报价页面
- `projectName` - 项目名称
- `projectCode` - 项目编号  
- `customerName` - 客户名称

**示例URL：**
```
/#/quotation/project-quotation?projectName=某商场空气质量检测项目
/#/quotation/project-quotation?projectCode=PROJ2024001
/#/quotation/project-quotation?projectName=水质检测&projectCode=PROJ2024002
```

### 采样任务页面
- `taskName` - 任务名称
- `taskCode` - 任务编号
- `status` - 任务状态

**示例URL：**
```
/#/sampling/task?taskName=公共场所空气质量检测任务001
/#/sampling/task?taskCode=TASK2024001
/#/sampling/task?taskName=水质检测&status=1
```

## 功能验证

### 1. 直接URL访问测试
访问带参数的URL，验证页面是否自动填充查询条件：

```
http://localhost:4000/#/quotation/project-quotation?projectName=测试项目
http://localhost:4000/#/sampling/task?taskName=测试任务
```

### 2. 履约情况跳转测试
1. 进入合同详情页面
2. 切换到"履约情况"标签页
3. 点击任务名称、项目名称、报价单编号链接
4. 验证跳转后的页面是否自动填充了查询条件

### 3. 测试页面
创建了专门的测试页面：`front/src/test/route-params-test.html`
包含各种测试链接和验证步骤。

## 技术细节

### Vue 3 Composition API (项目报价页面)
- 使用 `useRoute()` 获取路由对象
- 在 `onMounted()` 生命周期中处理参数
- 通过 `route.query` 访问URL参数

### Vue 2 Options API (采样任务页面)
- 使用 `this.$route` 访问路由对象
- 在 `created()` 生命周期中处理参数
- 通过 `this.$route.query` 访问URL参数

## 注意事项

1. **参数类型转换**：URL参数都是字符串类型，如果需要数字类型需要进行转换
2. **参数验证**：建议添加参数有效性验证，避免无效参数影响查询
3. **编码处理**：中文参数需要正确的URL编码/解码
4. **浏览器兼容性**：确保路由功能在目标浏览器中正常工作

## 扩展功能

未来可以考虑添加：
1. 参数持久化（刷新页面后保持查询条件）
2. 参数历史记录
3. 复杂查询条件的URL序列化
4. 查询条件的分享功能

## 测试结果

✅ 项目报价页面路由参数自动填充功能已修复
✅ 采样任务页面路由参数自动填充功能已修复  
✅ 履约情况跳转功能已优化
✅ 创建了完整的测试页面和文档

修复完成后，用户可以通过URL参数直接访问带有预设查询条件的页面，大大提升了用户体验和系统的易用性。
