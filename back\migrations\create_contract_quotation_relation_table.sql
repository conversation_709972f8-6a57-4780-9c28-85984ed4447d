-- 创建合同关联报价单表
CREATE TABLE contract_quotation_relation (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    contract_id INT NOT NULL COMMENT '合同ID',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编号',
    create_by VARCHAR(50) NULL COMMENT '创建人',
    create_time DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (contract_id) REFERENCES contract(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_contract_id (contract_id),
    INDEX idx_project_code (project_code),
    
    -- 唯一约束：同一合同不能关联同一项目编号
    UNIQUE KEY uk_contract_project (contract_id, project_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同关联报价单表';
