<template>
  <el-dialog
    title="采样任务分配"
    v-model="dialogVisible"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" class="mb-4">
      <el-step title="选择点位" description="选择需要分配的点位"></el-step>
      <el-step title="选择周期" description="选择检测周期条目"></el-step>
      <el-step title="确认分配" description="确认并创建任务"></el-step>
    </el-steps>

    <!-- 项目信息 -->
    <div v-if="quotation" class="assignment-header mb-4">
      <h3>{{ quotation.projectName }} ({{ quotation.projectCode }})</h3>
      <p class="text-muted">客户：{{ quotation.customerName }}</p>
    </div>

    <!-- 第一步：选择点位 -->
    <div v-if="currentStep === 0">
      <div v-if="confirmedPointItems.length === 0" class="no-data">
        <el-alert
          title="暂无已确认的点位"
          description="请先进行点位确认后再进行采样分配"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
      
      <div v-else>
        <div class="step-header mb-3">
          <h4>选择需要分配的点位</h4>
          <span class="text-muted">已确认点位：{{ confirmedPointItems.length }} 个</span>
        </div>
        
        <el-table
          :data="confirmedPointItems"
          @selection-change="handlePointSelectionChange"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="pointName" label="点位名称" min-width="150">
            <template #default="scope">
              {{ scope.row.pointName }}
              <el-tag v-if="scope.row.count > 1" type="info" size="small" class="ml-1">
                ×{{ scope.row.count }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sampleSource" label="样品来源" min-width="120"></el-table-column>
          <el-table-column prop="confirmedBy" label="确认人" width="100"></el-table-column>
          <el-table-column prop="confirmedTime" label="确认时间" width="150">
            <template #default="scope">
              {{ scope.row.confirmedTime ? new Date(scope.row.confirmedTime).toLocaleString() : '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 第二步：选择检测周期条目 -->
    <div v-if="currentStep === 1">
      <div v-if="cycleItems.length === 0" class="no-data">
        <el-alert
          title="暂无可分配的检测周期条目"
          description="所选点位暂无可分配的检测周期条目"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
      
      <div v-else>
        <div class="step-header mb-3">
          <h4>选择检测周期条目</h4>
          <div>
            <span class="text-muted">已选择点位：{{ selectedPointItems.length }} 个</span>
            <span class="ml-4 text-muted">可分配条目：{{ cycleItems.length }} 个</span>
          </div>
        </div>

        <!-- 批量操作按钮 -->
        <div class="batch-operation-buttons mb-3">
          <el-tooltip content="纵向选择所有检测项目的下一个可选周期" placement="top">
            <el-button 
              type="primary" 
              size="small" 
              icon="Select" 
              @click="handleBatchSelectNext"
              :disabled="filteredCycleItems.length === 0"
            >
              一键选择
            </el-button>
          </el-tooltip>
          <el-tooltip content="清空所有选择" placement="top">
            <el-button 
              type="warning" 
              size="small" 
              icon="RefreshLeft" 
              @click="handleBatchCancel"
              :disabled="selectedCycleItemIds.length === 0"
            >
              一键取消
            </el-button>
          </el-tooltip>
        </div>

        <!-- 检测项目搜索框 -->
        <div class="detection-search-box mb-3">
          <el-input
            v-model="detectionSearchKeyword"
            placeholder="搜索检测项目（类别、参数、方法）"
            prefix-icon="Search"
            clearable
            size="small"
            style="width: 300px;"
          />
          <span v-if="detectionSearchKeyword" class="search-result-count ml-2">
            找到 {{ filteredCycleItems.length }} / {{ cycleItems.length }} 个项目
          </span>
        </div>
        
        <!-- 检测项目列表 -->
        <div class="detection-items-container">
          <div v-for="item in filteredCycleItems" :key="item.itemId" class="detection-item-card">
            <div class="item-header">
              <strong>{{ item.category }} - {{ item.parameter }}</strong>
              <span class="item-info">点位：{{ item.pointName }} | 检测方法：{{ item.method }} | 周期类型：{{ item.cycleType }} | 总周期数：{{ item.cycleCount }}</span>
            </div>
            
            <div class="cycle-selection">
              <span class="cycle-label">周期：</span>
              <el-checkbox-group v-model="selectedCycleItemIds">
                <el-checkbox
                  v-for="cycle in item.cycleItems"
                  :key="cycle.id"
                  :value="cycle.id"
                  :disabled="!isCycleSelectable(item, cycle)"
                  @change="handleCycleChange(item, cycle, $event)"
                  class="cycle-checkbox"
                >
                  <div class="cycle-content" :class="{
                    'cycle-assigned': cycle.status === 1,
                    'cycle-completed': cycle.status === 2
                  }">
                    <div class="cycle-number">{{ cycle.cycleNumber }}</div>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三步：确认分配 -->
    <div v-if="currentStep === 2">
      <div class="step-header mb-3">
        <h4>确认任务信息</h4>
      </div>

      <el-form :model="assignmentForm" ref="assignmentForm" :rules="assignmentRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="assignmentForm.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsibleUserId">
              <el-select v-model="assignmentForm.responsibleUserId" placeholder="请选择负责人" clearable style="width: 100%">
                <el-option
                  v-for="user in userList"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="任务成员">
              <el-select
                v-model="assignmentForm.memberUserIds"
                placeholder="请选择任务成员"
                multiple
                clearable
                style="width: 100%"
                :max-collapse-tags="3"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="user in userList"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId"
                />
              </el-select>
              <div class="form-tip">选择参与此采样任务的成员，可以多选</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="任务描述" prop="taskDescription">
          <el-input
            v-model="assignmentForm.taskDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划开始时间">
              <el-date-picker
                v-model="assignmentForm.plannedStartTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划结束时间">
              <el-date-picker
                v-model="assignmentForm.plannedEndTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 任务成员预览 -->
      <div v-if="assignmentForm.memberUserIds && assignmentForm.memberUserIds.length > 0" class="member-preview">
        <h5>已选择的任务成员：</h5>
        <div class="member-tags">
          <el-tag
            v-for="userId in assignmentForm.memberUserIds"
            :key="userId"
            type="info"
            class="member-tag"
          >
            {{ getUserNameById(userId) }}
          </el-tag>
        </div>
      </div>

      <!-- 分配摘要 -->
      <div class="assignment-summary">
        <h5>分配摘要</h5>
        <p>选择点位：{{ selectedPointItems.length }} 个（{{ mergedPointNames.length }} 个不同点位）</p>
        <p>选择周期条目：{{ selectedCycleItemIds.length }} 个</p>
        <div v-if="mergedPointNames.length > 0" class="point-list">
          <strong>点位列表：</strong>
          <el-tag
            v-for="pointInfo in mergedPointNames"
            :key="pointInfo.name"
            class="mr-1 mb-1"
            :type="pointInfo.count > 1 ? 'warning' : ''"
          >
            {{ pointInfo.name }}
            <span v-if="pointInfo.count > 1" class="point-count">×{{ pointInfo.count }}</span>
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button v-if="currentStep > 0" @click="handlePrevStep">上一步</el-button>
        <el-button 
          v-if="currentStep < 2" 
          type="primary" 
          @click="handleNextStep"
          :disabled="!canProceedToNextStep"
        >
          下一步
        </el-button>
        <el-button 
          v-if="currentStep === 2" 
          type="primary" 
          @click="submitAssignment" 
          :loading="assignmentLoading"
        >
          生成采样任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { getConfirmedPointItems, getCycleItemsByPointItems, createSamplingTask } from "@/api/sampling/assignment";
import { listUser } from "@/api/system/user";

export default {
  name: 'SamplingAssignmentWizard',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    quotation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  data() {
    return {
      currentStep: 0,
      loading: false,
      assignmentLoading: false,
      
      // 点位相关
      confirmedPointItems: [],
      selectedPointItems: [],
      
      // 周期条目相关
      cycleItems: [],
      selectedCycleItemIds: [],
      detectionSearchKeyword: '',
      
      // 用户列表
      userList: [],
      
      // 表单数据
      assignmentForm: {
        taskName: '',
        taskDescription: '',
        responsibleUserId: null,
        plannedStartTime: null,
        plannedEndTime: null
      },
      
      // 表单验证规则
      assignmentRules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        responsibleUserId: [
          { required: true, message: '请选择负责人', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    
    canProceedToNextStep() {
      if (this.currentStep === 0) {
        return this.selectedPointItems.length > 0
      } else if (this.currentStep === 1) {
        return this.selectedCycleItemIds.length > 0
      }
      return true
    },
    
    filteredCycleItems() {
      if (!this.detectionSearchKeyword) {
        return this.cycleItems
      }

      const keyword = this.detectionSearchKeyword.toLowerCase()
      return this.cycleItems.filter(item =>
        item.category.toLowerCase().includes(keyword) ||
        item.parameter.toLowerCase().includes(keyword) ||
        item.method.toLowerCase().includes(keyword) ||
        item.pointName.toLowerCase().includes(keyword)
      )
    },

    // 合并相同名称的点位（后端已经做了合并，这里直接使用）
    mergedPointNames() {
      return this.selectedPointItems.map(point => ({
        name: point.pointName,
        count: point.count || 1
      })).sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.quotation) {
        this.initializeWizard()
      }
    }
  },
  methods: {
    async initializeWizard() {
      this.currentStep = 0
      this.selectedPointItems = []
      this.selectedCycleItemIds = []
      this.cycleItems = []
      this.detectionSearchKeyword = ''

      // 重置表单
      this.assignmentForm = {
        taskName: `${this.quotation.projectName}-采样任务`,
        taskDescription: '',
        responsibleUserId: null,
        memberUserIds: [],
        plannedStartTime: null,
        plannedEndTime: null
      }

      // 获取已确认点位列表
      await this.loadConfirmedPointItems()

      // 获取用户列表
      await this.loadUserList()
    },

    async loadConfirmedPointItems() {
      try {
        this.loading = true
        const response = await getConfirmedPointItems(this.quotation.id)
        this.confirmedPointItems = response.data || []

        if (this.confirmedPointItems.length === 0) {
          this.$message.warning('该项目暂无已确认的点位，请先进行点位确认')
        }
      } catch (error) {
        console.error('获取已确认点位失败:', error)
        this.$message.error('获取已确认点位失败')
      } finally {
        this.loading = false
      }
    },

    async loadUserList() {
      try {
        const response = await listUser()
        this.userList = response.rows || []
      } catch (error) {
        console.error('获取用户列表失败:', error)
      }
    },

    async loadCycleItemsByPoints() {
      if (this.selectedPointItems.length === 0) {
        this.cycleItems = []
        return
      }

      try {
        this.loading = true
        // 展开所有点位的ID（因为合并后的点位id是数组）
        const pointItemIds = []
        this.selectedPointItems.forEach(item => {
          if (Array.isArray(item.id)) {
            // 如果id是数组，展开所有ID
            pointItemIds.push(...item.id)
          } else {
            // 兼容旧数据格式
            pointItemIds.push(item.id)
          }
        })

        const response = await getCycleItemsByPointItems(pointItemIds)
        this.cycleItems = response.data || []

        if (this.cycleItems.length === 0) {
          this.$message.warning('所选点位暂无可分配的检测周期条目')
        }
      } catch (error) {
        console.error('获取检测周期条目失败:', error)
        this.$message.error('获取检测周期条目失败')
      } finally {
        this.loading = false
      }
    },

    handlePointSelectionChange(selection) {
      this.selectedPointItems = selection
    },

    async handleNextStep() {
      if (this.currentStep === 0) {
        if (this.selectedPointItems.length === 0) {
          this.$message.warning('请至少选择一个点位')
          return
        }

        // 加载选定点位的检测周期条目
        await this.loadCycleItemsByPoints()
        this.currentStep = 1
      } else if (this.currentStep === 1) {
        if (this.selectedCycleItemIds.length === 0) {
          this.$message.warning('请至少选择一个检测周期条目')
          return
        }

        this.currentStep = 2
      }
    },

    handlePrevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 判断周期是否可选择
    isCycleSelectable(item, cycle) {
      if (cycle.status !== 0) return false // 只有未分配状态才可选

      // 检查前面的周期是否都已分配
      const cycleIndex = item.cycleItems.findIndex(c => c.id === cycle.id)
      if (cycleIndex === 0) return true // 第一个周期总是可选

      // 检查前面的周期是否都已分配
      for (let i = 0; i < cycleIndex; i++) {
        if (item.cycleItems[i].status === 0) {
          return false
        }
      }

      return true
    },

    handleCycleChange(item, cycle, checked) {
      // 这里可以添加额外的逻辑，比如自动选择后续周期等
    },

    handleBatchSelectNext() {
      const newSelections = []

      this.filteredCycleItems.forEach(item => {
        // 找到第一个可选的周期
        const selectableCycle = item.cycleItems.find(cycle =>
          this.isCycleSelectable(item, cycle) && !this.selectedCycleItemIds.includes(cycle.id)
        )

        if (selectableCycle) {
          newSelections.push(selectableCycle.id)
        }
      })

      this.selectedCycleItemIds = [...this.selectedCycleItemIds, ...newSelections]

      if (newSelections.length > 0) {
        this.$message.success(`已选择 ${newSelections.length} 个周期条目`)
      } else {
        this.$message.info('没有可选择的周期条目')
      }
    },

    handleBatchCancel() {
      this.selectedCycleItemIds = []
      this.$message.success('已清空所有选择')
    },

    async submitAssignment() {
      // 验证表单
      try {
        await this.$refs.assignmentForm.validate()
      } catch (error) {
        this.$message.error('请完善任务信息')
        return
      }

      if (this.selectedCycleItemIds.length === 0) {
        this.$message.error('请选择检测周期条目')
        return
      }

      try {
        this.assignmentLoading = true

        // 验证必需数据
        if (!this.quotation || !this.quotation.id) {
          this.$message.error('项目报价信息缺失')
          return
        }

        if (!this.selectedCycleItemIds || this.selectedCycleItemIds.length === 0) {
          this.$message.error('请选择检测周期条目')
          return
        }

        const assignmentData = {
          projectQuotationId: this.quotation.id,
          taskName: this.assignmentForm.taskName,
          taskDescription: this.assignmentForm.taskDescription,
          responsibleUserId: this.assignmentForm.responsibleUserId,
          memberUserIds: this.assignmentForm.memberUserIds || [],
          plannedStartTime: this.assignmentForm.plannedStartTime,
          plannedEndTime: this.assignmentForm.plannedEndTime,
          selectedCycleItemIds: this.selectedCycleItemIds
        }

        console.log('提交采样任务分配数据:', JSON.stringify(assignmentData, null, 2))

        const response = await createSamplingTask(assignmentData)

        this.$message.success('采样任务创建成功')
        this.$emit('success', response.data)
        this.handleClose()
      } catch (error) {
        console.error('创建采样任务失败:', error)

        // 提取错误信息
        let errorMessage = '创建采样任务失败'
        if (error.response && error.response.data && error.response.data.msg) {
          errorMessage = error.response.data.msg
        } else if (error.message) {
          errorMessage = error.message
        }

        this.$message.error(errorMessage)
      } finally {
        this.assignmentLoading = false
      }
    },

    getUserNameById(userId) {
      const user = this.userList.find(u => u.userId === userId)
      return user ? user.nickName : `用户${userId}`
    },

    handleClose() {
      this.dialogVisible = false
      this.currentStep = 0
      this.selectedPointItems = []
      this.selectedCycleItemIds = []
      this.cycleItems = []
      this.detectionSearchKeyword = ''
    }
  }
}
</script>

<style scoped>
.assignment-header {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.assignment-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.assignment-header .text-muted {
  color: #909399;
  margin: 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.step-header h4 {
  margin: 0;
  color: #303133;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.batch-operation-buttons {
  display: flex;
  gap: 8px;
}

.detection-search-box {
  display: flex;
  align-items: center;
}

.search-result-count {
  color: #909399;
  font-size: 12px;
}

.detection-items-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
}

.detection-item-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.detection-item-card:last-child {
  margin-bottom: 0;
}

.item-header {
  margin-bottom: 8px;
}

.item-header strong {
  color: #303133;
  font-size: 14px;
}

.item-info {
  color: #909399;
  font-size: 12px;
  margin-left: 12px;
}

.cycle-selection {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.cycle-label {
  color: #606266;
  font-size: 13px;
  margin-right: 8px;
  min-width: 40px;
}

.cycle-checkbox {
  margin-right: 8px;
  margin-bottom: 4px;
}

.cycle-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.cycle-content:hover {
  border-color: #409eff;
}

.cycle-content.cycle-assigned {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.cycle-content.cycle-completed {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.cycle-number {
  font-size: 12px;
  font-weight: 500;
}

.assignment-summary {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 16px;
}

.assignment-summary h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.assignment-summary p {
  margin: 4px 0;
  color: #606266;
}

.point-list {
  margin-top: 8px;
}

.point-list strong {
  color: #303133;
  margin-right: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-muted {
  color: #909399;
}

.member-preview {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
  border-left: 4px solid #409eff;
}

.member-preview h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.member-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.member-tag {
  margin: 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.point-count {
  margin-left: 4px;
  font-weight: bold;
  color: #e6a23c;
}

.ml-1 {
  margin-left: 4px;
}
</style>
