-- 移除检测周期条目相关的触发器和存储过程
-- 将逻辑迁移到应用程序代码中

-- 1. 删除触发器
DROP TRIGGER IF EXISTS `tr_project_quotation_approved`;

-- 2. 删除存储过程
DROP PROCEDURE IF EXISTS `sp_generate_detection_cycle_items`;

-- 说明：
-- 触发器和存储过程的功能已经迁移到应用程序代码中
-- 具体实现在：
-- - back/module_quotation/service/project_quotation_approval_service.py 的 _generate_detection_cycle_items 方法
-- - back/module_sampling/service/detection_cycle_item_service.py 的 generate_cycle_items_for_quotation 方法
-- 
-- 这样做的好处：
-- 1. 业务逻辑统一在应用程序中管理
-- 2. 更容易调试和维护
-- 3. 更好的错误处理和日志记录
-- 4. 支持单元测试
-- 5. 避免数据库层面的复杂性
