<template>
  <div class="contract-detail">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="基本信息" name="basic">
          <!-- 一、基本信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">一、基本信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同名称">{{ contractData.contractName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同编号">{{ contractData.contractNumber || '-' }}</el-descriptions-item>
            <el-descriptions-item label="外部合同编号">{{ contractData.externalContractNumber || '-' }}</el-descriptions-item>
            <el-descriptions-item label="业务类型">{{ contractData.businessType || '-' }}</el-descriptions-item>
            <el-descriptions-item label="区域">
              {{ (contractData.regionProvince && contractData.regionCity) ?
                 `${contractData.regionProvince} ${contractData.regionCity}` : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="委托单位">{{ contractData.clientName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="委托单位联系人">{{ contractData.clientContact || '-' }}</el-descriptions-item>
            <el-descriptions-item label="取得方式">{{ contractData.acquisitionMethod || '-' }}</el-descriptions-item>
            <el-descriptions-item label="项目负责人">
              <span v-if="contractData.projectManagerNames && contractData.projectManagerNames.length > 0">
                <el-tag
                  v-for="(manager, index) in contractData.projectManagerNames"
                  :key="index"
                  size="small"
                  style="margin: 2px"
                  type="primary"
                >
                  {{ manager }}
                </el-tag>
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目客服">{{ contractData.projectServiceName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ contractData.deptName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同签订日期">{{ contractData.contractSignDate || '-' }}</el-descriptions-item>
          </el-descriptions>
          <!-- 二、金额信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">二、金额信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="金额类型">{{ contractData.amountType || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同金额">
              {{ contractData.contractAmount ? '¥' + Number(contractData.contractAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="报价单总金额">
              {{ contractData.quotationTotalAmount ? '¥' + Number(contractData.quotationTotalAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="变更后合同金额">
              {{ contractData.changedContractAmount ? '¥' + Number(contractData.changedContractAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="外协金额">
              {{ contractData.outsourcingAmount ? '¥' + Number(contractData.outsourcingAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="外协单位">{{ contractData.outsourcingCompany || '-' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 三、其他信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">三、其他信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="完工时间">{{ parseTime(contractData.completionTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同状态">
              <el-tag
                v-if="contractData.contractStatus"
                :type="getStatusType(contractData.contractStatus)"
              >
                {{ contractData.contractStatus }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">{{ contractData.createBy || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(contractData.createTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新人">{{ contractData.updateBy || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ parseTime(contractData.updateTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ contractData.remark || '-' }}</el-descriptions-item>
          </el-descriptions>
           <!-- 二、附件列表 -->
          <el-row v-if="attachmentList.length > 0">
            <el-col :span="23">
              <el-form-item label="已上传附件">
                <el-table :data="attachmentList" style="width: 100%" size="small">
                  <el-table-column prop="originalFilename" label="文件名" min-width="200" />
                  <el-table-column prop="fileSizeMb" label="文件大小" width="100">
                    <template #default="scope">
                      <span v-if="scope.row.fileSizeMb">{{ scope.row.fileSize }} MB</span>
                      <span v-else>{{scope.row.fileSize}} byte</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="uploadTime" label="上传时间" width="150" />
                  <el-table-column prop="createBy" label="上传人" width="100" />
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="downloadContractAttachment(scope.row)">
                        下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="关联报价单" name="quotation" v-if="contractData.id">
          <ContractQuotationRelation
            ref="contractQuotationRelationRef"
            :contract-id="contractData.id"
            :readonly="true"
          />
        </el-tab-pane>
        <el-tab-pane label="商务信息" name="business">
          <ContractBusiness
            :contract-id="contractData.id"
            :readonly="true"
          />
        </el-tab-pane>
        <el-tab-pane label="回款管理" name="payment" v-if="contractData.id">
          <ContractPaymentManagement
            :contract-id="contractData.id"
            :readonly="true"
          />
        </el-tab-pane>
        <el-tab-pane label="成本管理" name="cost" v-if="contractData.id">
          <ContractCostManagement
            :contract-id="contractData.id"
            :readonly="true"
          />
        </el-tab-pane>
        <el-tab-pane label="履约情况" name="performance" v-if="contractData.id">
          <ContractPerformanceDemo
            :contract-id="contractData.id"
          />
        </el-tab-pane>
      </el-tabs>

      <!-- 关闭按钮 -->
      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="cancel">关 闭</el-button>
      </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'
import ContractBusiness from './ContractBusiness.vue'
import ContractQuotationRelation from './ContractQuotationRelation.vue'
import ContractPaymentManagement from './ContractPaymentManagement.vue'
import ContractCostManagement from './ContractCostManagement.vue'
import ContractPerformanceDemo from './ContractPerformanceDemo.vue'
import { getAttachmentList, downloadAttachment } from "@/api/contract/attachment"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  contractData: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '合同详情'
  }
})

const emit = defineEmits(['update:visible', 'cancel'])

// 对话框显示状态
const open = ref(false)
const activeTab = ref('basic')
const attachmentList = ref([])




// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '草稿':
      return 'info'
    case '待签订':
      return 'warning'
    case '执行中':
      return 'primary'
    case '已完成':
      return 'success'
    case '已终止':
      return 'danger'
    default:
      return 'info'
  }
}

// 取消
const cancel = () => {
  open.value = false
  emit('cancel')
}

// 附件相关方法
const loadAttachmentList = async () => {
  if (!props.contractData.id) return

  try {
    const response = await getAttachmentList({
      contract_id: props.contractData.id,
      // page_num: 1,
      // page_size: 100
    })
    if (response.code === 200) {
      attachmentList.value = response.data.attachments || []
    } else {
      console.error('获取附件列表失败:', response.msg)
      ElMessage.error(response.msg || '获取附件列表失败')
    }
  } catch (error) {
    console.error('加载附件列表失败:', error)
  }
}
const downloadContractAttachment = async (attachment) => {
  try {
    const response = await downloadAttachment(attachment.id)
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', attachment.originalFilename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('文件下载失败')
  }
}


// 监听visible变化
watch(() => props.visible, (newVal) => {
  open.value = newVal
  console.log("visible:", newVal)
  if(newVal){

    console.log("ContractDetail: 加载附件列表")
    loadAttachmentList()
    // calculateQuotationTotalAmount(props.contractData.id).then(res => {
    //   if(res.code === 200){
    //     props.contractData.quotationTotalAmount = res.data
    //   }
    // })
  
  }
}, { immediate: true })
</script>

<style scoped>
.contract-detail {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-tab-pane) {
  overflow-y: auto;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-tag {
  margin: 2px;
}
</style>