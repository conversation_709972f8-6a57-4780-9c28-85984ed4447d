#!/usr/bin/env python3
"""
调试质控样功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dto.sample_record_dto import QualityControlSampleCreateDTO

async def test_quality_control():
    """测试质控样功能"""
    print("开始测试质控样功能...")
    
    # 获取数据库会话
    async for db in get_db():
        try:
            # 创建服务实例
            service = SampleRecordService(db)
            
            # 1. 检查原样品是否存在
            print("\n1. 检查原样品记录...")
            original_sample = await service.sample_record_dao.get_sample_record_by_id(1)
            if not original_sample:
                print("❌ 原样品记录不存在")
                return
            
            print(f"✅ 找到原样品记录: ID={original_sample.id}, 编号={original_sample.sample_number}")
            
            # 2. 创建质控样DTO
            print("\n2. 创建质控样...")
            create_dto = QualityControlSampleCreateDTO(
                original_sample_id=1,
                quality_control_types=["parallel_sample", "full_blank_sample"],
                point_name="测试点位1"
            )
            
            # 3. 调用服务方法
            try:
                results = await service.create_quality_control_samples(create_dto, 1)
                print(f"✅ 质控样创建成功，共创建 {len(results)} 个质控样")
                
                for result in results:
                    print(f"  - 质控样ID: {result.id}, 编号: {result.sample_number}, 类型: {result.quality_control_type}")
                
            except Exception as e:
                print(f"❌ 创建质控样失败: {str(e)}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
                
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
        finally:
            await db.close()
        break

if __name__ == "__main__":
    asyncio.run(test_quality_control())
