from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from utils.response_util import ResponseUtil
from module_sampling.service.detection_cycle_item_service import DetectionCycleItemService
from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO


router = APIRouter(prefix="/sampling/cycle-item", tags=["检测周期条目管理"])


@router.post("/generate/{project_quotation_id}", response_model=CrudResponseModel, summary="为项目报价生成检测周期条目")
async def generate_cycle_items_for_quotation(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """为项目报价生成检测周期条目"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.generate_cycle_items_for_quotation(project_quotation_id, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="生成检测周期条目成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.post("/regenerate/{project_quotation_id}", response_model=CrudResponseModel, summary="重新生成项目报价的检测周期条目")
async def regenerate_cycle_items_for_quotation(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """重新生成项目报价的检测周期条目"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.regenerate_cycle_items_for_quotation(project_quotation_id, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="重新生成检测周期条目成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/project/{project_quotation_id}", response_model=CrudResponseModel, summary="根据项目报价ID获取检测周期条目列表")
async def get_cycle_items_by_project_quotation_id(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据项目报价ID获取检测周期条目列表"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.get_cycle_items_by_project_quotation_id(project_quotation_id)
        return ResponseUtil.success(data=result, msg="获取检测周期条目列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/project/{project_quotation_id}/unassigned", response_model=CrudResponseModel, summary="根据项目报价ID获取未分配的检测周期条目列表")
async def get_unassigned_cycle_items_by_project_quotation_id(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据项目报价ID获取未分配的检测周期条目列表"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.get_unassigned_cycle_items_by_project_quotation_id(project_quotation_id)
        return ResponseUtil.success(data=result, msg="获取未分配检测周期条目列表成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/get/{item_id}", response_model=CrudResponseModel, summary="获取检测周期条目详情")
async def get_cycle_item(
    item_id: int = Path(..., description="条目ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """获取检测周期条目详情"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.get_cycle_item_by_id(item_id)
        if result:
            return ResponseUtil.success(data=result, msg="获取检测周期条目成功")
        else:
            return ResponseUtil.error(msg="检测周期条目不存在")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.get("/page", response_model=CrudResponseModel, summary="分页查询检测周期条目")
async def page_cycle_items(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    project_quotation_id: Optional[int] = Query(None, description="项目报价ID"),
    project_quotation_item_point_item_id: Optional[int] = Query(None, description="项目报价点位明细ID"),
    status: Optional[int] = Query(None, description="状态"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """分页查询检测周期条目"""
    try:
        service = DetectionCycleItemService(db)
        items, total = await service.page_cycle_items(
            page=page,
            size=size,
            project_quotation_id=project_quotation_id,
            project_quotation_item_point_item_id=project_quotation_item_point_item_id,
            status=status
        )
        
        result = {
            "records": items,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
        
        return ResponseUtil.success(data=result, msg="查询检测周期条目成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/status/{item_id}", response_model=CrudResponseModel, summary="更新检测周期条目状态")
async def update_cycle_item_status(
    item_id: int = Path(..., description="条目ID"),
    status: int = Body(..., description="新状态"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """更新检测周期条目状态"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.update_cycle_item_status(item_id, status, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="更新检测周期条目状态成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.put("/batch-status", response_model=CrudResponseModel, summary="批量更新检测周期条目状态")
async def batch_update_cycle_item_status(
    item_ids: List[int] = Body(..., description="条目ID列表"),
    status: int = Body(..., description="新状态"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """批量更新检测周期条目状态"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.batch_update_cycle_item_status(item_ids, status, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="批量更新检测周期条目状态成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@router.delete("/project/{project_quotation_id}", response_model=CrudResponseModel, summary="根据项目报价ID删除所有检测周期条目")
async def delete_cycle_items_by_project_quotation_id(
    project_quotation_id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据项目报价ID删除所有检测周期条目"""
    try:
        service = DetectionCycleItemService(db)
        result = await service.delete_cycle_items_by_project_quotation_id(project_quotation_id, current_user.user.user_id)
        return ResponseUtil.success(data=result, msg="删除检测周期条目成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))