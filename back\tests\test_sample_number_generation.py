"""
测试样品编号生成逻辑
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.service.sample_record_service import SampleRecordService
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from utils.category_identifier_util import CategoryIdentifierUtil


class TestSampleNumberGeneration:
    """测试样品编号生成"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def sample_record_service(self, mock_db):
        """创建样品记录服务实例"""
        service = SampleRecordService(mock_db)
        # 确保category_dao被初始化
        from module_basedata.dao.technical_manual_category_dao import TechnicalManualCategoryDao
        service.category_dao = AsyncMock(spec=TechnicalManualCategoryDao)
        return service
    
    @pytest.mark.asyncio
    async def test_generate_sample_number_with_water_category(self, sample_record_service):
        """测试生成水样品编号"""
        # 模拟类目查询返回水分类
        mock_category = MagicMock(spec=TechnicalManualCategory)
        mock_category.classification = "水"
        mock_category.category = "地表水"
        
        sample_record_service.category_dao.get_by_category = AsyncMock(return_value=mock_category)
        
        # 测试生成样品编号
        task_code = "2501001"
        detection_category = "地表水"
        sequence = 1
        
        sample_number = await sample_record_service._generate_sample_number(
            task_code, detection_category, sequence
        )
        
        # 验证样品编号格式：任务单号 + 类别标识(S) + 样品序号(001)
        assert sample_number == "2501001S001"
    
    @pytest.mark.asyncio
    async def test_generate_sample_number_with_soil_category(self, sample_record_service):
        """测试生成土壤样品编号"""
        # 模拟类目查询返回土分类
        mock_category = MagicMock(spec=TechnicalManualCategory)
        mock_category.classification = "土"
        mock_category.category = "土壤"
        
        sample_record_service.category_dao.get_by_category = AsyncMock(return_value=mock_category)
        
        # 测试生成样品编号
        task_code = "2501002"
        detection_category = "土壤"
        sequence = 5
        
        sample_number = await sample_record_service._generate_sample_number(
            task_code, detection_category, sequence
        )
        
        # 验证样品编号格式：任务单号 + 类别标识(T) + 样品序号(005)
        assert sample_number == "2501002T005"
    
    @pytest.mark.asyncio
    async def test_generate_sample_number_with_air_category(self, sample_record_service):
        """测试生成气样品编号"""
        # 模拟类目查询返回气分类
        mock_category = MagicMock(spec=TechnicalManualCategory)
        mock_category.classification = "气"
        mock_category.category = "环境空气"
        
        sample_record_service.category_dao.get_by_category = AsyncMock(return_value=mock_category)
        
        # 测试生成样品编号
        task_code = "2501003"
        detection_category = "环境空气"
        sequence = 10
        
        sample_number = await sample_record_service._generate_sample_number(
            task_code, detection_category, sequence
        )
        
        # 验证样品编号格式：任务单号 + 类别标识(Q) + 样品序号(010)
        assert sample_number == "2501003Q010"
    
    @pytest.mark.asyncio
    async def test_generate_sample_number_with_unknown_category(self, sample_record_service):
        """测试生成未知类别样品编号"""
        # 模拟类目查询返回None
        sample_record_service.category_dao.get_by_category = AsyncMock(return_value=None)
        
        # 测试生成样品编号
        task_code = "2501004"
        detection_category = "未知类别"
        sequence = 1
        
        sample_number = await sample_record_service._generate_sample_number(
            task_code, detection_category, sequence
        )
        
        # 验证样品编号格式：任务单号 + 默认类别标识(O) + 样品序号(001)
        assert sample_number == "2501004O001"
    
    @pytest.mark.asyncio
    async def test_generate_sample_number_with_public_place_category(self, sample_record_service):
        """测试生成公共场所样品编号"""
        # 模拟类目查询返回公共场所分类
        mock_category = MagicMock(spec=TechnicalManualCategory)
        mock_category.classification = "公共场所"
        mock_category.category = "公共场所卫生"
        
        sample_record_service.category_dao.get_by_category = AsyncMock(return_value=mock_category)
        
        # 测试生成样品编号
        task_code = "2501005"
        detection_category = "公共场所卫生"
        sequence = 3
        
        sample_number = await sample_record_service._generate_sample_number(
            task_code, detection_category, sequence
        )
        
        # 验证样品编号格式：任务单号 + 类别标识(GG) + 样品序号(003)
        assert sample_number == "2501005GG003"
    
    def test_category_identifier_util_water(self):
        """测试类别标识工具 - 水"""
        identifier = CategoryIdentifierUtil.get_identifier("水")
        assert identifier == "S"
    
    def test_category_identifier_util_soil(self):
        """测试类别标识工具 - 土"""
        identifier = CategoryIdentifierUtil.get_identifier("土")
        assert identifier == "T"
    
    def test_category_identifier_util_air(self):
        """测试类别标识工具 - 气"""
        identifier = CategoryIdentifierUtil.get_identifier("气")
        assert identifier == "Q"
    
    def test_category_identifier_util_sound(self):
        """测试类别标识工具 - 声"""
        identifier = CategoryIdentifierUtil.get_identifier("声")
        assert identifier == "S"
    
    def test_category_identifier_util_vibration(self):
        """测试类别标识工具 - 震动"""
        identifier = CategoryIdentifierUtil.get_identifier("震动")
        assert identifier == "Z"
    
    def test_category_identifier_util_public_place(self):
        """测试类别标识工具 - 公共场所"""
        identifier = CategoryIdentifierUtil.get_identifier("公共场所")
        assert identifier == "GG"
    
    def test_category_identifier_util_ecology(self):
        """测试类别标识工具 - 生态"""
        identifier = CategoryIdentifierUtil.get_identifier("生态")
        assert identifier == "ST"
    
    def test_category_identifier_util_unknown(self):
        """测试类别标识工具 - 未知分类"""
        identifier = CategoryIdentifierUtil.get_identifier("未知分类")
        assert identifier == "O"
    
    def test_category_identifier_util_empty(self):
        """测试类别标识工具 - 空字符串"""
        identifier = CategoryIdentifierUtil.get_identifier("")
        assert identifier == "O"
    
    def test_category_identifier_util_none(self):
        """测试类别标识工具 - None"""
        identifier = CategoryIdentifierUtil.get_identifier(None)
        assert identifier == "O"

