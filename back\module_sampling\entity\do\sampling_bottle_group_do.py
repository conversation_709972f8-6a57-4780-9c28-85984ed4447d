"""
采样瓶组表数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, ForeignKey, Index, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingBottleGroup(Base):
    """
    采样瓶组表
    """
    __tablename__ = 'sampling_bottle_group'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    bottle_maintenance_id = Column(Integer, ForeignKey('bottle_maintenance.id'), default=None, comment='瓶组管理ID，NULL表示默认瓶组')
    
    # 业务字段
    bottle_group_code = Column(String(50), nullable=False, unique=True, comment='瓶组编号')
    detection_method = Column(Text, comment='检测方法')
    sample_count = Column(Integer, default=0, comment='关联样品数量')
    status = Column(Integer, default=0, comment='状态：0-采样，1-装箱，2-流转，3-完成')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # sampling_task = relationship("SamplingTask", back_populates="bottle_groups")
    # bottle_maintenance = relationship("BottleMaintenance", back_populates="sampling_bottle_groups")
    # sample_relations = relationship("SamplingBottleGroupSample", back_populates="bottle_group", cascade="all, delete-orphan")
    
    # 创建人和更新人关系（暂时注释，避免循环导入）
    # creator = relationship("SysUser", foreign_keys=[create_by])
    # updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_task_id', 'sampling_task_id'),
        Index('idx_bottle_maintenance_id', 'bottle_maintenance_id'),
        Index('idx_detection_method', 'detection_method'),
        {'comment': '采样瓶组表'}
    )
    
    def __repr__(self):
        return f"<SamplingBottleGroup(id={self.id}, bottle_group_code='{self.bottle_group_code}', sampling_task_id={self.sampling_task_id}, bottle_maintenance_id={self.bottle_maintenance_id})>"
