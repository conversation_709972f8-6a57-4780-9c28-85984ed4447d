#!/usr/bin/env python3
"""
测试合同履约情况功能的脚本
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {"Authorization": "Bearer test_token"}

def test_get_contract_quotation_relations():
    """测试获取合同关联报价单列表"""
    print("测试获取合同关联报价单列表...")
    contract_id = 1  # 假设存在ID为1的合同
    url = f"{BASE_URL}/contract/quotation-relation/{contract_id}"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        print(f"获取关联报价单响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                relations = result.get('data', {}).get('rows', [])
                print(f"✅ 获取合同关联报价单成功，共 {len(relations)} 个关联项目")
                return relations
            else:
                print(f"❌ 获取合同关联报价单失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取合同关联报价单异常: {e}")
    
    return []

def test_get_sampling_tasks_enhanced():
    """测试获取采样任务增强版列表"""
    print("\n测试获取采样任务增强版列表...")
    url = f"{BASE_URL}/sampling/task/page-enhanced"
    
    params = {
        'page': 1,
        'size': 10,
        'project_code': 'TEST001'  # 假设的项目编号
    }
    
    try:
        response = requests.get(url, params=params, headers=HEADERS, timeout=30)
        print(f"获取任务列表响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                tasks = result.get('data', {}).get('records', [])
                print(f"✅ 获取采样任务列表成功，共 {len(tasks)} 个任务")
                return tasks
            else:
                print(f"❌ 获取采样任务列表失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取采样任务列表异常: {e}")
    
    return []

def test_get_project_quotation_by_id():
    """测试根据项目报价ID获取采样任务"""
    print("\n测试根据项目报价ID获取采样任务...")
    project_quotation_id = 1  # 假设存在ID为1的项目报价
    url = f"{BASE_URL}/sampling/task/project/{project_quotation_id}"
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        print(f"获取项目任务响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                tasks = result.get('data', [])
                print(f"✅ 根据项目报价ID获取任务成功，共 {len(tasks)} 个任务")
                return tasks
            else:
                print(f"❌ 根据项目报价ID获取任务失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 根据项目报价ID获取任务异常: {e}")
    
    return []

def test_contract_performance_workflow():
    """测试完整的合同履约情况工作流程"""
    print("\n测试完整的合同履约情况工作流程...")
    
    # 1. 获取合同关联的报价单
    relations = test_get_contract_quotation_relations()
    
    if not relations:
        print("❌ 没有找到合同关联的报价单，无法继续测试")
        return False
    
    # 2. 对每个关联的项目，获取对应的任务
    all_tasks = []
    for relation in relations:
        project_code = relation.get('projectCode')
        if project_code:
            print(f"\n获取项目 {project_code} 的任务...")
            # 这里可以通过项目编号查询任务
            tasks = test_get_sampling_tasks_enhanced()
            all_tasks.extend(tasks)
    
    print(f"\n✅ 履约情况工作流程测试完成，共获取到 {len(all_tasks)} 个任务")
    return True

def main():
    """主测试函数"""
    print("开始测试合同履约情况功能...")
    print("=" * 60)
    
    # 测试各个接口
    relations_success = len(test_get_contract_quotation_relations()) > 0
    tasks_success = len(test_get_sampling_tasks_enhanced()) >= 0  # 允许为空
    project_tasks_success = len(test_get_project_quotation_by_id()) >= 0  # 允许为空
    
    # 测试完整工作流程
    workflow_success = test_contract_performance_workflow()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"合同关联报价单接口: {'✅ 通过' if relations_success else '❌ 失败'}")
    print(f"采样任务增强版接口: {'✅ 通过' if tasks_success else '❌ 失败'}")
    print(f"项目报价任务接口: {'✅ 通过' if project_tasks_success else '❌ 失败'}")
    print(f"完整工作流程: {'✅ 通过' if workflow_success else '❌ 失败'}")
    
    total_tests = 4
    passed_tests = sum([relations_success, tasks_success, project_tasks_success, workflow_success])
    print(f"\n总体结果: {passed_tests}/{total_tests} 通过")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
