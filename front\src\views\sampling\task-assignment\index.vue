<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类别" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="请选择业务类别"
          clearable
          style="width: 200px"
        >
          <el-option label="一般采样" value="sampling" />
          <el-option label="送样" value="sample" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="quotationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报价单编号" align="center" prop="projectCode" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="业务类别" align="center" prop="businessType" width="100">
        <template #default="{ row }">
          <el-tag :type="row.businessType === 'sampling' ? 'primary' : 'success'">
            {{ row.businessType === 'sampling' ? '一般采样' : '送样' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="合同信息" align="center" width="200">
        <template #default="{ row }">
          <div class="contract-info">
            <div v-if="row.contractName" class="contract-name">
              <el-icon><Document /></el-icon>
              {{ row.contractName }}
            </div>
            <div v-if="row.contractNumber" class="contract-number">
              编号: {{ row.contractNumber }}
            </div>
            <div v-if="!row.contractName && !row.contractNumber" class="no-contract">
              <el-text type="info">暂无合同</el-text>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="方案详情" align="center" width="150">
        <template #default="{ row }">
          <div class="scheme-details">
            <div v-if="row.schemeDetails">
              <div class="detail-item">
                <span class="label">频次:</span>
                <span class="value">{{ row.schemeDetails.totalFrequency || 0 }}次</span>
              </div>
              <div class="detail-item">
                <span class="label">样品:</span>
                <span class="value">{{ row.schemeDetails.totalSampleCount || 0 }}个</span>
              </div>
            </div>
            <div v-else class="no-scheme">
              <el-text type="info">暂无方案</el-text>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分配状态" align="center" prop="assignmentStatus" width="100">
        <template #default="{ row }">
          <el-tag :type="getAssignmentStatusType(row.assignmentStatus)">
            {{ getAssignmentStatusText(row.assignmentStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="检测项目数" align="center" width="180">
        <template #default="{ row }">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              已分配: {{ row.assignedItemsCount }}<br>
              未分配: {{ row.unassignedItemsCount }}<br>
              总数: {{ row.allItemsCount }}
            </template>
            <el-progress :percentage="getAssignmentProgress(row)" :status="getProgressStatus(row)">
              {{ row.assignedItemsCount }}/{{ row.allItemsCount }}
            </el-progress>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="text" icon="Edit" @click="handleAssign(row)">采样分配</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="采样任务分配"
      v-model="assignDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <!-- 项目信息展示区域 -->
      <el-card class="project-info-card" style="margin-bottom: 20px;">
        <template #header>
          <div class="card-header">
            <span>项目信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="报价单编号">{{ selectedQuotation?.projectCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ selectedQuotation?.projectName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ selectedQuotation?.customerName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="业务类别">
            <el-tag :type="selectedQuotation?.businessType === 'sampling' ? 'primary' : 'success'">
              {{ selectedQuotation?.businessType === 'sampling' ? '一般采样' : '送样' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合同名称">{{ selectedQuotation?.contractName || '暂无合同' }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ selectedQuotation?.contractNumber || '暂无编号' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 方案详情 -->
        <div v-if="selectedQuotation?.schemeDetails" class="scheme-details-section" style="margin-top: 16px;">
          <h4 style="margin: 0 0 12px 0; color: var(--el-text-color-primary);">方案详情</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="detail-stat">
                <div class="stat-value">{{ selectedQuotation.schemeDetails.totalFrequency || 0 }}</div>
                <div class="stat-label">总频次</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-stat">
                <div class="stat-value">{{ selectedQuotation.schemeDetails.totalSampleCount || 0 }}</div>
                <div class="stat-label">总样品数</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-form :model="taskForm" ref="taskFormRef" :rules="taskRules" label-width="100px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            placeholder="请输入任务描述"
            :rows="2"
          />
        </el-form-item>

        <!-- 检测项目选择区域 -->
        <div class="detection-items-section">
          <div class="section-title">检测项目选择：</div>
          <el-card v-for="item in detectionItems" :key="item.id" class="detection-item-card">
            <div class="item-header">
              <el-checkbox v-model="item.selected" @change="handleItemSelect(item)">
                检测项目: {{ item.parameter }}
              </el-checkbox>
            </div>
            <div class="cycles-container" v-if="item.selected">
              <div class="cycles-label">可选周期:</div>
              <div class="cycles-list">
                <el-checkbox
                  v-for="cycle in item.cycles"
                  :key="cycle.id"
                  v-model="cycle.selected"
                  :disabled="!canSelectCycle(item, cycle)"
                >
                  {{ cycle.cycleNumber }}
                </el-checkbox>
              </div>
            </div>
          </el-card>
        </div>
      </el-form>

      <template #footer>
         <div class="dialog-footer">
           <el-button @click="assignDialogVisible = false">取消</el-button>
           <el-button type="primary" @click="handleCreateTask">生成采样任务</el-button>
         </div>
       </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { pageSamplingTask, createSamplingTask } from '@/api/sampling/samplingTask'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  projectCode: '',
  projectName: '',
  customerName: '',
  businessType: ''
})

// 列表数据
const loading = ref(false)
const quotationList = ref([])
const total = ref(0)
const selectedQuotations = ref([])  // 选中的项目报价

// 分配弹窗数据
const assignDialogVisible = ref(false)
const selectedQuotation = ref(null)
const taskForm = reactive({
  taskName: '',
  description: '',
  projectQuotationId: null
})

// 表单校验规则
const taskRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入任务描述', trigger: 'blur' }]
}

// 检测项目数据
const detectionItems = ref([])

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const response = await pageSamplingTask(queryParams)
    quotationList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置操作
const resetQuery = () => {
  queryParams.projectCode = ''
  queryParams.projectName = ''
  queryParams.customerName = ''
  queryParams.businessType = ''
  handleQuery()
}

// 多选框选中数据处理
const handleSelectionChange = (selection) => {
  selectedQuotations.value = selection
}

// 获取分配状态类型
const getAssignmentStatusType = (status) => {
  const types = {
    unassigned: 'warning',
    partial: 'primary',
    assigned: 'success'
  }
  return types[status] || 'info'
}

// 获取分配状态文本
const getAssignmentStatusText = (status) => {
  const texts = {
    unassigned: '未分配',
    partial: '部分分配',
    assigned: '已分配'
  }
  return texts[status] || '未知'
}

// 计算分配进度
const getAssignmentProgress = (row) => {
  if (!row.allItemsCount) return 0
  return Math.round((row.assignedItemsCount / row.allItemsCount) * 100)
}

// 获取进度条状态
const getProgressStatus = (row) => {
  if (row.assignedItemsCount === row.allItemsCount) return 'success'
  if (row.assignedItemsCount > 0) return ''
  return 'warning'
}

// 打开分配弹窗
const handleAssign = (row) => {
  selectedQuotation.value = row
  taskForm.projectQuotationId = row.id
  taskForm.taskName = `${row.projectName}-采样任务`
  taskForm.description = `${row.projectName}的采样任务`
  // 加载检测项目和周期数据
  loadDetectionItems(row.id)
  assignDialogVisible.value = true
}

// 加载检测项目和周期数据
const loadDetectionItems = async (quotationId) => {
  try {
    // TODO: 调用后端API获取检测项目和周期数据
    // 模拟数据结构
    detectionItems.value = [
      {
        id: 1,
        parameter: '重金属检测',
        selected: false,
        cycles: [
          { id: 1, cycleNumber: 1, selected: false },
          { id: 2, cycleNumber: 2, selected: false },
          { id: 3, cycleNumber: 3, selected: false }
        ]
      },
      {
        id: 2,
        parameter: 'pH值检测',
        selected: false,
        cycles: [
          { id: 4, cycleNumber: 1, selected: false },
          { id: 5, cycleNumber: 2, selected: false }
        ]
      }
    ]
  } catch (error) {
    console.error('加载检测项目失败:', error)
    ElMessage.error('加载检测项目失败')
  }
}

// 处理检测项目选择
const handleItemSelect = (item) => {
  if (!item.selected) {
    // 取消选择时，清空所有周期选择
    item.cycles.forEach(cycle => cycle.selected = false)
  }
}

// 判断是否可以选择周期
const canSelectCycle = (item, cycle) => {
  if (!item.selected) return false
  // 检查是否有未选择的较早周期
  const earlierCycles = item.cycles.filter(c => c.cycleNumber < cycle.cycleNumber)
  return !earlierCycles.some(c => !c.selected)
}

// 创建采样任务
const handleCreateTask = async () => {
  // 收集选中的检测项目和周期
  const selectedItems = detectionItems.value
    .filter(item => item.selected)
    .map(item => ({
      itemId: item.id,
      cycles: item.cycles
        .filter(cycle => cycle.selected)
        .map(cycle => cycle.id)
    }))

  if (selectedItems.length === 0) {
    ElMessage.warning('请至少选择一个检测项目及其周期')
    return
  }

  try {
    await createSamplingTask({
      ...taskForm,
      items: selectedItems
    })
    ElMessage.success('采样任务创建成功')
    assignDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('创建采样任务失败:', error)
    ElMessage.error('创建采样任务失败')
  }
}

// 页面加载时获取列表数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
    h2 {
      margin: 0;
      font-size: 24px;
      color: var(--el-text-color-primary);
    }
  }

  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: var(--el-bg-color);
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
  }

  .detection-items-section {
    margin-top: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .detection-item-card {
      margin-bottom: 15px;

      .item-header {
        margin-bottom: 10px;
      }

      .cycles-container {
        padding-left: 24px;
        margin-top: 10px;

        .cycles-label {
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
        }

        .cycles-list {
          display: flex;
          gap: 20px;
        }
      }
    }
  }

  // 合同信息样式
  .contract-info {
    .contract-name {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 13px;
      color: var(--el-text-color-primary);

      .el-icon {
        margin-right: 4px;
        color: var(--el-color-primary);
      }
    }

    .contract-number {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }

    .no-contract {
      font-size: 12px;
    }
  }

  // 方案详情样式
  .scheme-details {
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;

      .label {
        color: var(--el-text-color-regular);
        min-width: 40px;
      }

      .value {
        color: var(--el-text-color-primary);
        font-weight: 500;
      }
    }

    .no-scheme {
      font-size: 12px;
    }
  }

  // 项目信息卡片样式
  .project-info-card {
    .card-header {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .scheme-details-section {
      background-color: var(--el-bg-color-page);
      padding: 16px;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);

      .detail-stat {
        text-align: center;
        padding: 12px;
        background-color: var(--el-bg-color);
        border-radius: 6px;
        border: 1px solid var(--el-border-color-lighter);

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
</style>