"""
样品记录数据访问层
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import and_, or_, desc, asc, select, func, delete
from datetime import datetime

from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_admin.entity.do.user_do import SysUser


class SampleRecordDAO:
    """样品记录数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_sample_record(self, sample_record: SampleRecord) -> SampleRecord:
        """创建样品记录"""
        self.db.add(sample_record)
        await self.db.flush()
        await self.db.refresh(sample_record)
        return sample_record
    
    async def batch_create_sample_records(self, sample_records: List[SampleRecord]) -> List[SampleRecord]:
        """批量创建样品记录"""
        self.db.add_all(sample_records)
        await self.db.flush()
        for record in sample_records:
            await self.db.refresh(record)
        return sample_records
    
    async def get_sample_record_by_id(self, record_id: int) -> Optional[SampleRecord]:
        """根据ID获取样品记录"""
        stmt = select(SampleRecord).where(SampleRecord.id == record_id)

        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    


    async def get_sample_records_by_group_id(self, group_id: int) -> List[SampleRecord]:
        """根据任务分组ID获取样品记录列表"""
        # 使用基础查询，避免复杂的关联加载导致的问题
        stmt = select(SampleRecord).where(
            SampleRecord.sampling_task_group_id == group_id
        ).order_by(SampleRecord.sample_number)

        result = await self.db.execute(stmt)
        return result.scalars().all()


    
    async def update_sample_record(self, sample_record: SampleRecord) -> SampleRecord:
        """更新样品记录"""
        await self.db.merge(sample_record)
        await self.db.flush()
        await self.db.refresh(sample_record)
        return sample_record
    
    async def update_sample_record_status(self, record_id: int, status: int, update_by: int) -> bool:
        """更新样品记录状态"""
        stmt = select(SampleRecord).where(SampleRecord.id == record_id)
        result = await self.db.execute(stmt)
        sample_record = result.scalar_one_or_none()
        
        if sample_record:
            sample_record.status = status
            sample_record.update_by = update_by
            sample_record.update_time = datetime.now()
            
            # 根据状态更新相应的时间字段
            if status == 1:  # 已采集
                sample_record.collection_time = datetime.now()
            elif status == 2:  # 已送检
                sample_record.submission_time = datetime.now()
            elif status == 4:  # 已完成
                sample_record.completion_time = datetime.now()
            
            await self.db.flush()
            return True
        return False
    
    async def delete_sample_record(self, record_id: int) -> bool:
        """删除样品记录"""
        stmt = delete(SampleRecord).where(SampleRecord.id == record_id)
        result = await self.db.execute(stmt)
        await self.db.flush()
        return result.rowcount > 0
    
    async def delete_sample_records_by_assignment_id(self, assignment_id: int) -> int:
        """根据执行指派ID删除样品记录"""
        stmt = delete(SampleRecord).where(SampleRecord.sampling_task_assignment_id == assignment_id)
        result = await self.db.execute(stmt)
        await self.db.flush()
        return result.rowcount
    
    # 注意：此方法已废弃，因为sample_number现在是字符串类型
    # async def get_max_sample_number_by_assignment(self, assignment_id: int) -> int:
    #     """获取指定执行指派下的最大样品序号"""
    #     stmt = select(func.max(SampleRecord.sample_number)).where(
    #         SampleRecord.sampling_task_assignment_id == assignment_id
    #     )
    #     result = await self.db.execute(stmt)
    #     max_number = result.scalar()
    #     return max_number if max_number is not None else 0
    
    async def count_sample_records_by_assignment(self, assignment_id: int) -> int:
        """统计指定执行指派下的样品记录数量"""
        stmt = select(func.count(SampleRecord.id)).where(
            SampleRecord.sampling_task_assignment_id == assignment_id
        )
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    
    async def get_sample_records_by_status(self, status: int) -> List[SampleRecord]:
        """根据状态获取样品记录列表"""
        stmt = select(SampleRecord).where(
            SampleRecord.status == status
        ).order_by(SampleRecord.create_time.desc())

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_quality_control_samples_by_original_id(self, original_sample_id: int) -> List[SampleRecord]:
        """根据原样品ID获取所有质控样"""
        stmt = select(SampleRecord).where(
            and_(
                SampleRecord.is_quality_control == True,
                SampleRecord.related_sample_id == original_sample_id
            )
        ).order_by(SampleRecord.sample_number)

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def count_quality_control_samples_by_original_id(self, original_sample_id: int) -> int:
        """统计指定原样品的质控样数量"""
        stmt = select(func.count(SampleRecord.id)).where(
            and_(
                SampleRecord.is_quality_control == True,
                SampleRecord.related_sample_id == original_sample_id
            )
        )

        result = await self.db.execute(stmt)
        return result.scalar() or 0

    # 注意：此方法已废弃，因为sample_number现在是字符串类型，不再需要获取最大序号
    # async def get_max_sample_number_by_group_id(self, group_id: int) -> int:
    #     """获取指定分组的最大样品序号"""
    #     stmt = select(func.max(SampleRecord.sample_number)).where(
    #         SampleRecord.sampling_task_group_id == group_id
    #     )
    #
    #     result = await self.db.execute(stmt)
    #     max_number = result.scalar()
    #     return max_number or 0

    async def get_sample_records_by_group_id(self, group_id: int) -> List[SampleRecord]:
        """根据分组ID获取样品记录列表"""
        stmt = select(SampleRecord).where(
            SampleRecord.sampling_task_group_id == group_id
        ).order_by(SampleRecord.sample_number)

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_samples_with_quality_controls_by_group_id(self, group_id: int) -> List[SampleRecord]:
        """获取分组的所有样品记录（包含质控样）"""
        stmt = select(SampleRecord).where(
            SampleRecord.sampling_task_group_id == group_id
        ).order_by(SampleRecord.sample_number)

        result = await self.db.execute(stmt)
        return result.scalars().all()
