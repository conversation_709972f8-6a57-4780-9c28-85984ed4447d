"""
测试样品记录API接口
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:9099"

# 测试token（跳过认证）
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}


def test_get_samples_with_quality_controls():
    """测试获取分组的所有样品记录（包含质控样信息）"""
    print("=" * 80)
    print("测试获取分组的所有样品记录（包含质控样信息）")
    print("=" * 80)

    group_id = 29  # 使用前端错误日志中的分组ID
    
    print(f"\n1. 获取分组 {group_id} 的样品记录...")
    response = requests.get(
        f"{BASE_URL}/sampling/sample-records/group/{group_id}/with-quality-controls",
        headers=HEADERS
    )
    
    print(f"   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"\n   ✅ 成功获取样品记录")
    else:
        print(f"   ❌ 请求失败")
        print(f"   错误信息: {response.text}")


def test_get_sample_records_by_group():
    """测试获取任务分组的样品记录列表"""
    print("\n" + "=" * 80)
    print("测试获取任务分组的样品记录列表")
    print("=" * 80)

    group_id = 29
    
    print(f"\n1. 获取分组 {group_id} 的样品记录...")
    response = requests.get(
        f"{BASE_URL}/sampling/sample-records/group/{group_id}",
        headers=HEADERS
    )
    
    print(f"   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"\n   ✅ 成功获取样品记录")
    else:
        print(f"   ❌ 请求失败")
        print(f"   错误信息: {response.text}")


if __name__ == "__main__":
    test_get_samples_with_quality_controls()
    test_get_sample_records_by_group()

