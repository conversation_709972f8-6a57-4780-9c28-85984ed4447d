<template>
  <div class="bottle-maintenance">
    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :model="queryForm" :inline="true" label-width="100px">
        <el-form-item label="瓶组编码">
          <el-input
            v-model="queryForm.bottleCode"
            placeholder="请输入瓶组编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="容器类型">
          <el-input
            v-model="queryForm.bottleType"
            placeholder="请输入容器类型"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="容器容量">
          <el-input
            v-model="queryForm.bottleVolume"
            placeholder="请输入容器容量"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="检测参数">
          <el-input
            v-model="queryForm.parameter"
            placeholder="请输入技术手册的检测参数"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="检测方法">
          <el-input
            v-model="queryForm.method"
            placeholder="请输入技术手册的检测参数方法"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            查询
          </el-button>
          <el-button @click="resetQuery">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="operation-card">
      <el-button type="primary" @click="handleAdd">
        新增
      </el-button>
      <el-button type="success" @click="handleImport">
        批量导入
      </el-button>
      <el-button type="info" @click="handleDownloadTemplate">
        下载模板
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="expand-content">
              <h4>关联的技术手册</h4>
              <el-table
                :data="row.technicalManuals"
                border
                size="small"
                style="width: 100%, margin: 10px 2px"
              >
                <el-table-column prop="category" label="类别" width="200" />
                <el-table-column prop="parameter" label="参数" width="200" />
                <el-table-column prop="method" label="方法"  />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="bottleCode" label="瓶组编码" width="120" sortable />
        <el-table-column prop="bottleType" label="容器类型" width="120" sortable />
        <el-table-column prop="bottleVolume" label="容器容量" width="120" sortable />
        <el-table-column prop="storageStyles" label="存储方式" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="style in row.storageStyles"
              :key="style"
              size="small"
              style="margin-right: 5px"
            >
              {{ style }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fixStyles" label="固定方式" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="style in row.fixStyles"
              :key="style"
              size="small"
              type="success"
              style="margin-right: 5px"
            >
              {{ style }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="样品时效" width="120">
          <template #default="{ row }">
            {{ row.sampleAge }}{{ row.sampleAgeUnit }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 新增/编辑对话框 -->
    <AddEditDialog
      :visible="dialogVisible"
      :form-data="currentRow"
      :is-edit="isEdit"
      @update:visible="dialogVisible = $event"
      @success="handleDialogSuccess"
    />

    <!-- 批量导入对话框 -->
    <ImportDialog
      :visible="importDialogVisible"
      @update:visible="importDialogVisible = $event"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 图标按需导入
// import { Search, Refresh, Plus, Upload, Download } from '@element-plus/icons-vue'
import {
  getBottleMaintenanceList,
  deleteBottleMaintenance,
  downloadImportTemplate
} from '@/api/basedata/bottleMaintenance'
import AddEditDialog from './components/AddEditDialog.vue'
import ImportDialog from './components/ImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const isEdit = ref(false)
const currentRow = ref({})

// 查询表单
const queryForm = reactive({
  bottleCode: '',
  bottleType: '',
  bottleVolume: '',
  parameter: '',
  method: '',
  category: ''
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    
    const response = await getBottleMaintenanceList(params)
    tableData.value = response.data.rows
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  pagination.pageNum = 1
  getList()
}

const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = ''
  })
  handleQuery()
}

const handleAdd = () => {
  currentRow.value = {}
  isEdit.value = false
  dialogVisible.value = true
}

const handleEdit = (row) => {
  currentRow.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除瓶组编码为"${row.bottleCode}"的记录吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteBottleMaintenance(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleImport = () => {
  importDialogVisible.value = true
}

const handleDownloadTemplate = async () => {
  try {
    const response = await downloadImportTemplate()
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '瓶组管理_导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('下载模板失败')
  }
}

const handleExpandChange = (row, expandedRows) => {
  // 展开行变化时的处理
  console.log('展开行变化:', row, expandedRows)
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getList()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getList()
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  getList()
}

const handleImportSuccess = () => {
  importDialogVisible.value = false
  getList()
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.bottle-maintenance {
  padding: 20px;
}

.search-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.expand-content {
  padding: 20px;
  background-color: #f5f7fa;
}

.expand-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}
</style>
