import request from '@/utils/request'

// 查询合同关联报价单列表
export function listContractQuotationRelation(contractId) {
  return request({
    url: `/contract/quotation-relation/${contractId}`,
    method: 'get'
  })
}

// 新增合同关联报价单
export function addContractQuotationRelation(contractId, data) {
  return request({
    url: `/contract/quotation-relation/${contractId}`,
    method: 'post',
    data: data
  })
}

// 修改合同关联报价单
export function updateContractQuotationRelation(contractId, data) {
  return request({
    url: `/contract/quotation-relation/${contractId}`,
    method: 'put',
    data: data
  })
}

// 删除接口已移除，因为前端改为全量保存模式，通过更新接口来处理删除操作
