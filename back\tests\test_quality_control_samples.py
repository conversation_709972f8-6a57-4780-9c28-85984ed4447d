"""
质控样功能单元测试
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dto.sample_record_dto import QualityControlSampleCreateDTO


class TestQualityControlSamples:
    """质控样功能测试类"""

    @pytest.fixture
    async def sample_record_service(self, db_session: AsyncSession):
        """创建样品记录服务实例"""
        return SampleRecordService(db_session)

    @pytest.fixture
    async def original_sample(self, db_session: AsyncSession):
        """创建原始样品记录"""
        sample = SampleRecord(
            group_id=1,
            sample_number=1,
            sample_type="地表水",
            sample_source="河流",
            point_name="测试点位1",
            detection_category="水质",
            detection_parameter="pH,COD",
            detection_method="GB/T 6920-1986",
            cycle_number=1,
            cycle_type="月",
            status=0,
            is_quality_control=False,
            create_by=1,
            update_by=1
        )
        db_session.add(sample)
        await db_session.commit()
        await db_session.refresh(sample)
        return sample

    async def test_create_quality_control_samples(self, sample_record_service, original_sample):
        """测试创建质控样"""
        # 准备测试数据
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=original_sample.id,
            quality_control_types=["parallel_sample", "full_blank_sample"],
            point_name="测试点位1"
        )

        # 执行创建质控样
        quality_control_samples = await sample_record_service.create_quality_control_samples(create_dto)

        # 验证结果
        assert len(quality_control_samples) == 2
        
        # 验证第一个质控样（平行样）
        parallel_sample = quality_control_samples[0]
        assert parallel_sample.is_quality_control is True
        assert parallel_sample.quality_control_type == "parallel_sample"
        assert parallel_sample.related_sample_id == original_sample.id
        assert parallel_sample.sample_number == 2  # 应该是下一个序号
        assert parallel_sample.sample_type == original_sample.sample_type
        assert parallel_sample.sample_source == original_sample.sample_source
        assert parallel_sample.detection_category == original_sample.detection_category
        assert parallel_sample.detection_parameter == original_sample.detection_parameter
        assert parallel_sample.detection_method == original_sample.detection_method

        # 验证第二个质控样（全程空白样）
        blank_sample = quality_control_samples[1]
        assert blank_sample.is_quality_control is True
        assert blank_sample.quality_control_type == "full_blank_sample"
        assert blank_sample.related_sample_id == original_sample.id
        assert blank_sample.sample_number == 3

    async def test_get_quality_control_samples_by_original_id(self, sample_record_service, original_sample):
        """测试根据原样品ID获取质控样"""
        # 先创建质控样
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=original_sample.id,
            quality_control_types=["parallel_sample"],
            point_name="测试点位1"
        )
        await sample_record_service.create_quality_control_samples(create_dto)

        # 获取质控样
        quality_control_samples = await sample_record_service.get_quality_control_samples_by_original_id(original_sample.id)

        # 验证结果
        assert len(quality_control_samples) == 1
        assert quality_control_samples[0].quality_control_type == "parallel_sample"
        assert quality_control_samples[0].related_sample_id == original_sample.id

    async def test_get_samples_with_quality_controls_by_group_id(self, sample_record_service, original_sample):
        """测试获取包含质控样信息的样品列表"""
        # 先创建质控样
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=original_sample.id,
            quality_control_types=["parallel_sample", "full_blank_sample"],
            point_name="测试点位1"
        )
        await sample_record_service.create_quality_control_samples(create_dto)

        # 获取包含质控样信息的样品列表
        samples = await sample_record_service.get_samples_with_quality_controls_by_group_id(original_sample.group_id)

        # 验证结果
        assert len(samples) == 3  # 1个原样品 + 2个质控样

        # 找到原样品
        original_sample_in_list = next((s for s in samples if s.id == original_sample.id), None)
        assert original_sample_in_list is not None
        assert original_sample_in_list.quality_control_count == 2
        assert original_sample_in_list.is_quality_control is False

        # 验证质控样
        quality_control_samples = [s for s in samples if s.is_quality_control]
        assert len(quality_control_samples) == 2
        
        for qc_sample in quality_control_samples:
            assert qc_sample.related_sample_id == original_sample.id
            assert qc_sample.related_sample_number == original_sample.sample_number
            assert qc_sample.quality_control_type_label in ["平行样", "全程空白样"]

    async def test_quality_control_api_endpoints(self, async_client: AsyncClient, original_sample):
        """测试质控样API接口"""
        # 测试创建质控样接口
        create_data = {
            "originalSampleId": original_sample.id,
            "qualityControlTypes": ["parallel_sample", "matrix_spike_sample"],
            "pointName": "测试点位1"
        }
        
        response = await async_client.post(
            "/sampling/sample-records/quality-control/create",
            json=create_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert len(result["data"]) == 2

        # 测试获取质控样接口
        response = await async_client.get(
            f"/sampling/sample-records/quality-control/original/{original_sample.id}",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert len(result["data"]) == 2

        # 测试获取包含质控样的样品列表接口
        response = await async_client.get(
            f"/sampling/sample-records/group/{original_sample.group_id}/with-quality-controls",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert len(result["data"]) == 3  # 1个原样品 + 2个质控样

    async def test_quality_control_type_validation(self, sample_record_service, original_sample):
        """测试质控样类型验证"""
        # 测试无效的质控样类型
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=original_sample.id,
            quality_control_types=["invalid_type"],
            point_name="测试点位1"
        )

        # 应该抛出异常或返回错误
        with pytest.raises(Exception):
            await sample_record_service.create_quality_control_samples(create_dto)

    async def test_sample_number_increment(self, sample_record_service, original_sample, db_session: AsyncSession):
        """测试样品编号递增逻辑"""
        # 添加另一个样品到同一分组
        another_sample = SampleRecord(
            group_id=original_sample.group_id,
            sample_number=5,  # 设置一个较大的编号
            sample_type="地表水",
            sample_source="河流",
            point_name="测试点位2",
            detection_category="水质",
            detection_parameter="pH",
            detection_method="GB/T 6920-1986",
            cycle_number=1,
            cycle_type="月",
            status=0,
            is_quality_control=False,
            create_by=1,
            update_by=1
        )
        db_session.add(another_sample)
        await db_session.commit()

        # 创建质控样
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=original_sample.id,
            quality_control_types=["parallel_sample"],
            point_name="测试点位1"
        )
        
        quality_control_samples = await sample_record_service.create_quality_control_samples(create_dto)
        
        # 验证样品编号应该是6（比最大编号5大1）
        assert quality_control_samples[0].sample_number == 6
