[build-system]
requires = ["setuptools>=42.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.poetry]
name = "ruoyi-fastapi"
version = "1.6.2"
description = "RuoYi FastAPI Backend"
authors = ["insistence <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
fastapi = "^0.104.1"
uvicorn = "^0.23.2"
sqlalchemy = "^2.0.23"
pydantic = "^2.4.2"
pydantic-settings = "^2.0.3"
python-jose = "^3.3.0"
python-multipart = "^0.0.6"
passlib = "^1.7.4"
asyncpg = "^0.28.0"
asyncmy = "^0.2.8"
redis = "^5.0.1"
apscheduler = "^3.10.4"
python-dotenv = "^1.0.0"
alembic = "^1.12.1"
psycopg2-binary = "^2.9.9"
pymysql = "^1.1.0"
pandas = "^2.1.3"
openpyxl = "^3.1.2"
sqlglot = "^19.9.0"

[tool.poetry.dev-dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"

[tool.black]
line-length = 120
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 120

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
asyncio_mode = "auto"
