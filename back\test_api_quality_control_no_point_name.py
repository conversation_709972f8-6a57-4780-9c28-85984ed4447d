"""
测试质控样API（不需要点位名称）
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:9099"

# 测试token（跳过认证）
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}


def test_create_quality_control_samples():
    """测试创建质控样（不需要点位名称）"""
    print("=" * 80)
    print("测试质控样创建API（不需要点位名称）")
    print("=" * 80)
    
    # 1. 创建质控样（不包含点位名称）
    print("\n1. 创建质控样（不需要输入点位名称）...")
    create_data = {
        "originalSampleId": 1,
        "qualityControlTypes": ["parallel_sample", "full_blank_sample"]
    }
    
    print(f"   请求数据: {json.dumps(create_data, indent=2, ensure_ascii=False)}")
    
    response = requests.post(
        f"{BASE_URL}/sampling/sample-records/quality-control/create",
        headers=HEADERS,
        json=create_data
    )
    
    print(f"\n   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get("code") == 200:
            print(f"\n✅ 质控样创建成功！")
            data = result.get("data", [])
            print(f"   共创建 {len(data)} 个质控样:")
            
            for i, sample in enumerate(data, 1):
                print(f"\n   质控样 {i}:")
                print(f"   - ID: {sample.get('id')}")
                print(f"   - 样品编号: {sample.get('sampleNumber')}")
                print(f"   - 点位名称: {sample.get('pointName')}")
                print(f"   - 质控样类型: {sample.get('qualityControlType')}")
                print(f"   - 质控样类型标签: {sample.get('qualityControlTypeLabel')}")
                print(f"   - 关联原样品ID: {sample.get('relatedSampleId')}")
                print(f"   - 关联原样品编号: {sample.get('relatedSampleNumber')}")
        else:
            print(f"\n❌ 创建失败: {result.get('msg')}")
    else:
        print(f"\n❌ 请求失败: {response.text}")
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)


if __name__ == "__main__":
    test_create_quality_control_samples()

