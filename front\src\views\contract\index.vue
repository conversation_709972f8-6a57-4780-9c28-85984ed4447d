<template>
  <div class="app-container">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="contract-tabs">
      <el-tab-pane label="全部合同" name="all"></el-tab-pane>
      <el-tab-pane label="我的合同" name="my"></el-tab-pane>
    </el-tabs>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNumber">
        <el-input
          v-model="queryParams.contractNumber"
          placeholder="请输入合同编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="请选择业务类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="委托单位" prop="clientName">
        <el-input
          v-model="queryParams.clientName"
          placeholder="请输入委托单位"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目负责人" prop="projectManager">
        <el-input
          v-model="queryParams.projectManager"
          placeholder="请输入项目负责人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同状态" prop="contractStatus">
        <el-select
          v-model="queryParams.contractStatus"
          placeholder="请选择合同状态"
          clearable
          style="width: 200px"
        >
          <el-option label="草稿" value="草稿" />
          <el-option label="待签订" value="待签订" />
          <el-option label="执行中" value="执行中" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已终止" value="已终止" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="contractList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="客户名称" align="center" prop="clientName" min-width="150" show-overflow-tooltip />
      <el-table-column label="项目名称" align="center" prop="contractName" min-width="180" show-overflow-tooltip />
      <el-table-column label="项目类型" align="center" prop="businessType" min-width="100" />
      <el-table-column label="合同编号" align="center" prop="contractNumber" min-width="130" show-overflow-tooltip />
      <el-table-column label="合同金额" align="center" prop="contractAmount" min-width="120">
        <template #default="scope">
          <span v-if="scope.row.contractAmount">¥{{ Number(scope.row.contractAmount).toLocaleString() }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center" prop="deptName" min-width="100" show-overflow-tooltip />
      <el-table-column label="项目负责人" align="center" prop="projectManagerNames" min-width="120" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.projectManagerNames && scope.row.projectManagerNames.length > 0">
            {{ scope.row.projectManagerNames.join(', ') }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="项目客服" align="center" prop="projectServiceName" min-width="100" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 查看合同详情对话框 -->
    <el-dialog title="合同详情" v-model="viewOpen" width="1200px" append-to-body>
      <ContractDetail
        v-if="viewOpen"
        :visible="viewOpen"
        :contract-data="viewData"
        @cancel="viewOpen = false"
      />
    </el-dialog>

    <!-- 添加或修改合同对话框 -->
    <el-dialog :title="title" v-model="editOpen" width="1200px" append-to-body :destroy-on-close="true">
      <ContractForm
        v-if="editOpen"
        :contract-data="editForm"
        :visible="editOpen"
        @submit="handleFormSubmit"
        @cancel="cancel"
      />
    </el-dialog>

    
  </div>
</template>

<script setup name="Contract">
import { watch, nextTick } from 'vue'
import {
  listContract,
  myContract,
  getContract,
  delContract,
  batchDelContract,
} from "@/api/contract/contract"

// 直接导入组件，避免异步加载导致的数据传递问题
import ContractForm from './components/ContractForm.vue'
import ContractDetail from './components/ContractDetail.vue'

const { proxy } = getCurrentInstance()

const contractList = ref([])
const editOpen = ref(false)
const viewOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

// 标签页相关
const activeTab = ref('all')

// 业务类型选项
const businessTypeOptions = ref([
  { label: "一般委托", value: "一般委托" },
  { label: "年度", value: "年度" },
  { label: "验收", value: "验收" },
  { label: "送样", value: "送样" },
  { label: "监督监测", value: "监督监测" },
  { label: "应急监测", value: "应急监测" },
  { label: "环评检测", value: "环评检测" },
  { label: "场调检测", value: "场调检测" },
  { label: "地下水专项", value: "地下水专项" },
  { label: "生态调查", value: "生态调查" },
  { label: "辐射", value: "辐射" },
  { label: "油气回收", value: "油气回收" },
  { label: "LADR", value: "LADR" },
  { label: "咨询服务", value: "咨询服务" },
  { label: "数字化服务", value: "数字化服务" },
  { label: "监理", value: "监理" },
  { label: "其他技术服务", value: "其他技术服务" },
  { label: "其他", value: "其他" },
  { label: "预留1", value: "预留1" },
  { label: "预留2", value: "预留2" },
  { label: "预留3", value: "预留3" }
])

const data = reactive({
  editForm: {},
  viewData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    contractName: null,
    contractNumber: null,
    businessType: null,
    clientName: null,
    projectManager: null,
    contractStatus: null
  }
})

const { queryParams, editForm, viewData } = toRefs(data)

/** 查询合同列表 */
function getList() {
  loading.value = true

  // 根据标签页设置查询参数
  const params = { ...queryParams.value }

  // 根据当前标签页过滤数据
  switch (activeTab.value) {
    case 'my':
      // 我的合同：当前用户是项目负责人或客服
      params.myContracts = true
      myContract(params).then(response => {
        contractList.value = response.data.rows
        total.value = response.data.total
        loading.value = false
      })
      break
    default:
      // 全部合同
      listContract(params).then(response => {
          contractList.value = response.data.rows
          total.value = response.data.total
          loading.value = false
      })
      break
  }

}

watch(activeTab, (newVal) => {
  console.log("activeTab changed:", newVal)
  queryParams.value.pageNum = 1
  getList(newVal)
})

/** 标签页切换 */
const handleTabClick = (tab) => {
  console.log("handleTabClick:", tab.name)
}

/** 取消按钮 */
function cancel() {
  editOpen.value = false
  reset()
  getList()
}

/** 表单重置 */
function reset() {
  viewData.value = {}
  editForm.value = {}
  viewOpen.value = false
  editOpen.value = false
  proxy.resetForm("contractRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  editOpen.value = true
  title.value = "添加合同"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const contractId = row.id || ids.value[0]
  title.value = "修改合同"

  // 先获取数据，再显示弹框
  getContract(contractId).then(response => {
    editForm.value = response.data
    // 使用nextTick确保数据更新后再显示弹框
    nextTick(() => {
      editOpen.value = true
    })
  }).catch(error => {
    console.error('获取合同数据失败:', error)
    proxy.$modal.msgError("获取合同数据失败")
  })
}

/** 查看按钮操作 */
function handleView(row) {
  const contractId = row.id
  getContract(contractId).then(response => {
    viewData.value = response.data
    viewOpen.value = true
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const contractIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除合同名称为"' + (row.contractName || contractIds) + '"的数据项？').then(function() {
    if (Array.isArray(contractIds)) {
      return batchDelContract(contractIds)
    } else {
      return delContract(contractIds)
    }
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 表单提交 */
function handleFormSubmit() {
  editOpen.value = false
  getList()
}

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case '草稿':
      return 'info'
    case '待签订':
      return 'warning'
    case '执行中':
      return 'primary'
    case '已完成':
      return 'success'
    case '已终止':
      return 'danger'
    default:
      return 'info'
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.contract-tabs {
  margin-bottom: 20px;
}

.warning-badge {
  margin-left: 5px;
}

.amount-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount-warning {
  margin-top: 4px;
  font-size: 10px;
}
</style>
