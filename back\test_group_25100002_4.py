"""
查询分组编号为 25100002-4 的样品信息
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.sample_record_do import SampleRecord


async def main():
    # 创建数据库连接
    engine = create_async_engine(
        "mysql+aiomysql://root:Wjl%2C.123@127.0.0.1:3306/lims?charset=utf8mb4",
        echo=False
    )
    
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        # 查询分组信息
        print("=" * 80)
        print("查询分组编号为 25100002-4 的信息")
        print("=" * 80)
        
        stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.group_code == '25100002-4')
        result = await session.execute(stmt)
        group = result.scalar_one_or_none()
        
        if not group:
            print("未找到分组编号为 25100002-4 的分组")
            return
        
        print(f"\n分组ID: {group.id}")
        print(f"分组编号: {group.group_code}")
        print(f"任务ID: {group.task_id}")
        print(f"分组名称: {group.group_name}")
        
        # 查询该分组的样品记录
        print("\n" + "=" * 80)
        print(f"查询分组 {group.id} 的样品记录")
        print("=" * 80)
        
        stmt = select(SampleRecord).where(
            SampleRecord.sampling_task_group_id == group.id
        ).order_by(SampleRecord.sample_number)
        
        result = await session.execute(stmt)
        samples = result.scalars().all()
        
        print(f"\n找到 {len(samples)} 条样品记录:\n")
        
        for sample in samples:
            print(f"样品ID: {sample.id}")
            print(f"  样品编号: {sample.sample_number}")
            print(f"  样品类型: {sample.sample_type}")
            print(f"  采样来源: {sample.sample_source}")
            print(f"  点位名称: {sample.point_name}")
            print(f"  检测类别: {sample.detection_category}")
            print(f"  检测参数: {sample.detection_parameter}")
            print(f"  是否质控样: {sample.is_quality_control}")
            print(f"  质控样类型: {sample.quality_control_type}")
            print(f"  关联样品ID: {sample.related_sample_id}")
            print(f"  状态: {sample.status}")
            print("-" * 80)
    
    await engine.dispose()


if __name__ == "__main__":
    asyncio.run(main())

