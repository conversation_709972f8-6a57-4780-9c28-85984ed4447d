<template>
  <div>
    <!-- 批量导入按钮 -->
    <el-button type="primary" @click="openImportDialog">
      <el-icon><Upload /></el-icon>
      批量导入
    </el-button>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入技术手册"
      width="600px"
      :close-on-click-modal="false"
      @close="resetImportDialog"
    >
      <div class="import-content">
        <!-- 开始导入区域 -->
        <div class="upload-section">
          <h4>开始导入</h4>
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :limit="1"
            :accept="'.xlsx,.xls'"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 下载模板区域 -->
        <div class="template-section">
          <h4>下载Excel模板</h4>
          <p class="template-description">
            请先下载模板，按照模板格式填写数据后再上传
          </p>
          <el-button type="success" @click="downloadTemplate" :loading="templateLoading">
            <el-icon><Download /></el-icon>
            下载模板
          </el-button>
          
          <!-- 模板字段说明 -->
          <div class="template-fields">
            <h5>模板字段说明：</h5>
            <ul>
              <li><strong>分类</strong>：检测分类（如：气、水、土壤等）</li>
              <li style="color:red"><strong>检测类别</strong>：具体检测类别（必填）, 多个英文逗号分隔</li>
              <li><strong>检测参数</strong>：检测参数名称（必填）</li>
              <li><strong>检测方法</strong>：检测方法描述（必填）</li>
              <li style="color:red"><strong>唯一编号</strong>：资质唯一编号，全局唯一</li>
              <li><strong>限制范围</strong>：检测限制范围</li>
              <li style="color:red"><strong>常用别名</strong>：参数的常用别名, 多个英文逗号分隔</li>
              <li><strong>是否资质</strong>：填写"有"或"无"，也可填写"0"或"1"</li>
              <li><strong>取得资质时间</strong>：支持格式：2025/1/2、2025-01-02等</li>
              <li><strong>分析类型</strong>：填写"实验室分析"或"现场直读"，也可填写"LAB_CHECK"或"ON_SITE_CHECK"</li>
            </ul>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport" :loading="importing" :disabled="!selectedFile">
            开始导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDialogVisible"
      title="导入错误详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="error-content">
        <div class="error-summary">
          <el-alert
            :title="`导入完成：总计 ${importResult.totalCount} 条，成功 ${importResult.successCount} 条，失败 ${importResult.errorCount} 条`"
            :type="importResult.errorCount > 0 ? 'warning' : 'success'"
            show-icon
            :closable="false"
          />
        </div>

        <div v-if="importResult.errorCount > 0" class="error-table">
          <h4>错误明细：</h4>
          <el-table :data="importResult.errors" style="width: 100%" border max-height="400">
            <el-table-column label="错误行" prop="rowNumber" width="80" align="center" />
            <el-table-column label="错误原因" prop="errorMessage" width="200">
              <template #default="scope">
                <el-tooltip :content="scope.row.errorMessage" placement="top">
                  <div class="error-message">{{ scope.row.errorMessage }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="资质编号" prop="qualificationCode" width="120" />
            <el-table-column label="检测类别" prop="category" width="120" />
            <el-table-column label="检测参数" prop="parameter" width="120" />
            <el-table-column label="检测方法" prop="method" min-width="200">
              <template #default="scope">
                <el-tooltip :content="scope.row.method" placement="top">
                  <div class="method-text">{{ scope.row.method }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="errorDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, UploadFilled, Download } from '@element-plus/icons-vue'
import { downloadImportTemplate, importTechnicalManual } from '@/api/basedata/technicalManual'

// 定义事件
const emit = defineEmits(['import-success'])

// 响应式数据
const importDialogVisible = ref(false)
const errorDialogVisible = ref(false)
const templateLoading = ref(false)
const importing = ref(false)
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)

// 导入结果
const importResult = ref({
  success: false,
  totalCount: 0,
  successCount: 0,
  errorCount: 0,
  errors: []
})

// 打开导入对话框
function openImportDialog() {
  importDialogVisible.value = true
}

// 重置导入对话框
function resetImportDialog() {
  fileList.value = []
  selectedFile.value = null
  importing.value = false
  uploadRef.value?.clearFiles()
}

// 处理文件选择
function handleFileChange(file) {
  selectedFile.value = file.raw
}

// 处理文件数量超限
function handleExceed() {
  ElMessage.warning('只能选择一个文件进行导入')
}

// 下载模板
async function downloadTemplate() {
  try {
    templateLoading.value = true
    const response = await downloadImportTemplate()
    
    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '技术手册导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  } finally {
    templateLoading.value = false
  }
}

// 开始导入
async function startImport() {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    importing.value = true
    const response = await importTechnicalManual(selectedFile.value)
    
    importResult.value = response.data
    
    if (importResult.value.success) {
      ElMessage.success(`导入成功！共导入 ${importResult.value.successCount} 条记录`)
      importDialogVisible.value = false
      emit('import-success')
    } else {
      if (importResult.value.errorCount > 0) {
        // 有错误，显示错误详情
        errorDialogVisible.value = true
        importDialogVisible.value = false
      } else {
        ElMessage.error('导入失败，请检查文件格式')
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    if (error.response && error.response.data && error.response.data.message) {
      ElMessage.error(`导入失败：${error.response.data.message}`)
    } else {
      ElMessage.error('导入失败，请检查文件格式和网络连接')
    }
  } finally {
    importing.value = false
  }
}
</script>

<style scoped>
.import-content {
  padding: 20px 0;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.upload-dragger {
  width: 100%;
}

.template-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.template-description {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.template-fields {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.template-fields h5 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.template-fields ul {
  margin: 0;
  padding-left: 20px;
}

.template-fields li {
  margin-bottom: 8px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.error-content {
  padding: 10px 0;
}

.error-summary {
  margin-bottom: 20px;
}

.error-table h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.error-message {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

.method-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
