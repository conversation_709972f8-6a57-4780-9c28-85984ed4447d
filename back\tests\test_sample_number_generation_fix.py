"""
测试样品编号生成修复
验证分组生成样品时使用正确的样品编号格式
"""
import pytest


def test_sample_number_generation_logic():
    """
    测试样品编号生成逻辑
    """
    from utils.category_identifier_util import CategoryIdentifierUtil
    
    # 测试用例
    test_cases = [
        {
            "task_code": "25100005",
            "detection_category": "水",
            "sequence": 1,
            "expected": "25100005S001",
            "description": "任务25100005的第1个水样品"
        },
        {
            "task_code": "25100005",
            "detection_category": "水",
            "sequence": 5,
            "expected": "25100005S005",
            "description": "任务25100005的第5个水样品"
        },
        {
            "task_code": "25100005",
            "detection_category": "气",
            "sequence": 1,
            "expected": "25100005Q001",
            "description": "任务25100005的第1个气样品"
        },
        {
            "task_code": "25100005",
            "detection_category": "固体废物",
            "sequence": 2,
            "expected": "25100005O002",
            "description": "任务25100005的第2个固体废物样品"
        },
        {
            "task_code": "25100005",
            "detection_category": "公共场所",
            "sequence": 10,
            "expected": "25100005GG010",
            "description": "任务25100005的第10个公共场所样品"
        }
    ]
    
    print("\n" + "="*80)
    print("样品编号生成逻辑测试")
    print("="*80)
    
    for test_case in test_cases:
        task_code = test_case["task_code"]
        detection_category = test_case["detection_category"]
        sequence = test_case["sequence"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        # 获取类别标识
        category_identifier = CategoryIdentifierUtil.get_identifier(detection_category)
        
        # 生成样品编号
        sample_number = f"{task_code}{category_identifier}{sequence:03d}"
        
        # 验证结果
        assert sample_number == expected, f"样品编号生成错误: 期望 {expected}, 实际 {sample_number}"
        
        print(f"\n✅ {description}")
        print(f"   任务编号: {task_code}")
        print(f"   检测类别: {detection_category}")
        print(f"   类别标识: {category_identifier}")
        print(f"   样品序号: {sequence}")
        print(f"   样品编号: {sample_number}")
    
    print("\n" + "="*80)
    print("✅ 所有测试用例通过！")
    print("="*80)


def test_sample_number_format_explanation():
    """
    解释样品编号格式
    """
    print("\n" + "="*80)
    print("样品编号格式说明")
    print("="*80)
    
    print("\n格式：任务单号(8位) + 类别标识(1-2位) + 样品序号(3位)")
    print("\n示例：")
    print("  - 25100005S001")
    print("    └─ 2510: 年月(25年10月)")
    print("    └─ 0005: 任务流水号(第5个任务)")
    print("    └─ S: 类别标识(水)")
    print("    └─ 001: 样品序号(第1个样品)")
    
    print("\n类别标识映射：")
    from utils.category_identifier_util import CategoryIdentifierUtil
    
    categories = [
        "水", "气", "土壤", "固体废物", "噪声", "振动",
        "公共场所", "职业卫生", "辐射", "生物", "其他"
    ]
    
    for category in categories:
        identifier = CategoryIdentifierUtil.get_identifier(category)
        print(f"  - {category:8s} → {identifier}")
    
    print("\n" + "="*80)


def test_problem_analysis():
    """
    分析问题：为什么分组编号为25100005-5的任务样品编号是1
    """
    print("\n" + "="*80)
    print("问题分析")
    print("="*80)
    
    print("\n问题描述：")
    print("  分组编号为 25100005-5 的任务，样品编号显示为 1")
    
    print("\n问题原因：")
    print("  在 generate_sample_records_for_group() 方法中（第133行）")
    print("  样品编号直接使用了简单的序号：sample_number = i + 1")
    print("  而不是按照设计的格式生成完整的样品编号")
    
    print("\n修复方案：")
    print("  1. 添加 _generate_sample_number() 方法")
    print("  2. 在生成样品记录时调用该方法生成完整的样品编号")
    print("  3. 样品编号格式：任务单号 + 类别标识 + 样品序号（3位）")
    
    print("\n修复后的效果：")
    print("  - 修复前：sample_number = 1")
    print("  - 修复后：sample_number = 25100005S001")
    
    print("\n" + "="*80)
    print("✅ 问题已定位并修复！")
    print("="*80)


if __name__ == "__main__":
    test_sample_number_generation_logic()
    test_sample_number_format_explanation()
    test_problem_analysis()

