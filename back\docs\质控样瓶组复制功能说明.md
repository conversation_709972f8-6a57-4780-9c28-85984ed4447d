# 质控样瓶组复制功能说明

## 功能概述

在创建质控样时，系统会自动复制原样品的所有瓶组关联信息，确保质控样与原样品使用相同的瓶组配置。

## 功能特性

### 1. 自动复制瓶组关联

当为某个样品创建质控样时，系统会：
- 查找原样品关联的所有瓶组
- 为每个质控样创建相同的瓶组关联
- 自动更新瓶组的样品数量统计

### 2. 支持的质控样类型

- 平行样 (parallel_sample)
- 全程空白样 (full_blank_sample)
- 运输空白样 (transport_blank_sample)
- 设备清洗空白样 (equipment_blank_sample)
- 基体加标样 (matrix_spike_sample)
- 实验室平行样 (lab_parallel_sample)

### 3. 数据一致性保证

- 质控样的瓶组关联与原样品完全一致
- 瓶组的样品数量自动更新
- 所有操作在同一事务中完成，保证数据一致性

## 实现细节

### 后端实现

#### 1. 数据模型

**瓶组表 (sampling_bottle_group)**
- id: 瓶组ID
- sampling_task_id: 采样任务ID
- bottle_group_code: 瓶组编号
- sample_count: 关联样品数量
- ...

**瓶组样品关联表 (sampling_bottle_group_sample)**
- id: 主键ID
- bottle_group_id: 瓶组ID
- sample_record_id: 样品记录ID
- create_time: 创建时间

#### 2. 核心方法

**SampleRecordService.create_quality_control_samples()**
```python
async def create_quality_control_samples(self, create_dto: QualityControlSampleCreateDTO, create_by: int) -> List[SampleRecordDTO]:
    """创建质控样"""
    # 1. 获取原样品记录
    # 2. 创建质控样记录
    # 3. 复制原样品的瓶组关联
    # 4. 提交事务
    # 5. 返回结果
```

**SampleRecordService._copy_bottle_groups_for_quality_control_samples()**
```python
async def _copy_bottle_groups_for_quality_control_samples(self, original_sample_id: int, quality_control_sample_ids: List[int]):
    """为质控样复制原样品的瓶组关联"""
    # 1. 查询原样品的瓶组关联
    # 2. 为每个质控样创建相同的瓶组关联
    # 3. 更新瓶组的样品数量
```

**SamplingBottleGroupDAO.update_bottle_group_sample_count()**
```python
async def update_bottle_group_sample_count(self, bottle_group_id: int, sample_count: int) -> bool:
    """更新瓶组的样品数量"""
    # 更新指定瓶组的样品数量字段
```

#### 3. 执行流程

```
1. 用户请求创建质控样
   ↓
2. 验证原样品是否存在
   ↓
3. 创建质控样记录
   ↓
4. 查询原样品的瓶组关联
   ↓
5. 为每个质控样复制瓶组关联
   ↓
6. 统计并更新瓶组的样品数量
   ↓
7. 提交事务
   ↓
8. 返回创建结果
```

### 前端实现

#### Web端

在样品管理界面中，用户可以：
1. 点击样品行的"添加质控样"按钮
2. 在弹出对话框中选择质控样类型（可多选）
3. 点击确定创建质控样

创建成功后，质控样会自动继承原样品的所有信息，包括瓶组关联。

#### 小程序端

在任务详情页面的样品列表中，用户可以：
1. 点击样品的"添加质控样"按钮
2. 选择需要的质控样类型（可多选）
3. 点击确定创建质控样

## 测试验证

### 测试场景

1. **原样品有瓶组关联**
   - 原样品关联2个瓶组（TEST-BG-001, TEST-BG-002）
   - 每个瓶组初始样品数为1

2. **创建质控样**
   - 创建2个质控样（平行样、全程空白样）
   - 样品编号自动递增

3. **验证结果**
   - ✅ 每个质控样都关联了2个瓶组
   - ✅ 瓶组编号与原样品一致
   - ✅ 瓶组样品数量更新：1 → 3（原样品1个 + 质控样2个）

### 测试日志

```
2025-10-27 00:04:38.263 | INFO | 为 2 个质控样复制了 2 个瓶组关联
2025-10-27 00:04:38.269 | INFO | 更新了 2 个瓶组的样品数量
2025-10-27 00:04:38.283 | INFO | 成功创建 2 个质控样，并复制了瓶组信息
```

## API接口

### 创建质控样

**接口地址**: `POST /sampling/sample-records/quality-control/create`

**请求参数**:
```json
{
  "originalSampleId": 1,
  "qualityControlTypes": ["parallel_sample", "full_blank_sample"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "质控样创建成功，共创建 2 个质控样",
  "data": [
    {
      "id": 29,
      "sampleNumber": 6,
      "qualityControlType": "parallel_sample",
      "relatedSampleId": 1,
      ...
    },
    {
      "id": 30,
      "sampleNumber": 7,
      "qualityControlType": "full_blank_sample",
      "relatedSampleId": 1,
      ...
    }
  ]
}
```

### 获取样品的瓶组列表

**接口地址**: `GET /sampling/bottle-groups/sample/{sample_id}`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 32,
      "bottleGroupCode": "TEST-BG-001",
      "sampleCount": 3,
      ...
    },
    {
      "id": 33,
      "bottleGroupCode": "TEST-BG-002",
      "sampleCount": 3,
      ...
    }
  ]
}
```

## 注意事项

1. **事务一致性**: 质控样创建和瓶组复制在同一事务中完成，如果任何步骤失败，所有操作都会回滚

2. **瓶组数量更新**: 系统会自动统计每个瓶组关联的样品数量并更新，无需手动维护

3. **原样品无瓶组**: 如果原样品没有关联任何瓶组，质控样也不会有瓶组关联，但不会影响质控样的创建

4. **错误处理**: 瓶组复制过程中的错误会被记录到日志，但不会阻止质控样的创建

## 相关文件

### 后端文件
- `back/module_sampling/service/sample_record_service.py` - 样品记录服务
- `back/module_sampling/dao/sampling_bottle_group_dao.py` - 瓶组数据访问对象
- `back/module_sampling/dao/sampling_bottle_group_sample_dao.py` - 瓶组样品关联数据访问对象
- `back/module_sampling/entity/do/sampling_bottle_group_do.py` - 瓶组数据模型
- `back/module_sampling/entity/do/sampling_bottle_group_sample_do.py` - 瓶组样品关联数据模型

### 前端文件
- `front/src/views/sampling/execution/index.vue` - Web端样品管理界面
- `miniprogram-native/pages/sampling/task-detail.js` - 小程序任务详情页面
- `miniprogram-native/pages/sampling/task-detail.wxml` - 小程序任务详情页面模板

### 测试文件
- `back/test_api_bottle_groups.py` - API测试脚本
- `back/test_quality_control_bottle_groups.py` - 功能测试脚本

## 更新日志

### 2025-10-27
- ✅ 实现质控样瓶组自动复制功能
- ✅ 添加瓶组样品数量自动更新功能
- ✅ 完成API测试验证
- ✅ 添加功能文档

