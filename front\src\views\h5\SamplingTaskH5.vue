<template>
  <div class="h5-sampling-task">
    <!-- 头部 -->
    <div class="header">
      <h2>采样任务</h2>
      <div class="task-number">分组编号：{{ taskData.groupCode }}</div>
      <div class="task-status">
        <label>任务状态：</label>
        <span class="status" :class="getStatusClass(taskData.status)">
          {{ getStatusLabel(taskData.status) }}
        </span>
      </div>
      <!-- 网络状态提示 -->
      <div v-if="!isOnline" class="offline-indicator">
        <el-icon><Connection /></el-icon>
        <span>离线模式</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
      <div class="error-actions">
        <el-button v-if="!error.includes('权限')" type="primary" size="small" @click="retryLoad">重试</el-button>
        <el-button size="small" @click="goToPCVersion">PC端查看</el-button>
        <el-button size="small" @click="goToTaskList">返回任务列表</el-button>
      </div>
    </div>

    <!-- Tab切换 -->
    <div v-else-if="taskData.id" class="tab-container">
      <div class="tabs">
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'task' }"
          @click="activeTab = 'task'"
        >
          任务信息
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'samples' }"
          @click="activeTab = 'samples'"
        >
          样品管理
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'point' }"
          @click="activeTab = 'point'"
        >
          点位信息
        </div>
      </div>

      <!-- Tab内容 -->
      <div class="tab-content">
        <!-- 任务信息 -->
        <div v-show="activeTab === 'task'" class="content">
          <div class="section">
            <h3 class="section-title">基础信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <label>分组编号：</label>
                <span>{{ taskData.groupCode || '-' }}</span>
              </div>
              <div class="info-item">
                <label>任务名称：</label>
                <span>{{ taskData.taskName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>项目名称：</label>
                <span>{{ taskData.projectName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>客户名称：</label>
                <span>{{ taskData.customerName || '-' }}</span>
              </div>
              <div class="info-item">
                <label>周期序号：</label>
                <span>{{ taskData.cycleNumber || '-' }}</span>
              </div>
              <div class="info-item">
                <label>周期类型：</label>
                <span>{{ taskData.cycleType || '-' }}</span>
              </div>
              <div class="info-item">
                <label>检测类别：</label>
                <span>{{ taskData.detectionCategory || '-' }}</span>
              </div>
              <div class="info-item">
                <label>点位名称：</label>
                <span>{{ taskData.pointName || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 样品管理 -->
        <div v-show="activeTab === 'samples'" class="content">
          <SampleManagementH5
            :task-data="taskData"
            :sample-records="sampleRecords"
            :sample-statistics="sampleStatistics"
            @refresh="loadSampleData"
          />
        </div>

        <!-- 点位信息 -->
        <div v-show="activeTab === 'point'" class="content">
          <PointInfoH5
            :group-id="taskData.id"
            :task-data="taskData"
            @refresh="loadTaskData"
          />
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p>扫码时间：{{ new Date().toLocaleString() }}</p>
      <p>采样管理系统</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, Warning, Connection } from '@element-plus/icons-vue'
import { getGroupDetail, getUserGroups } from '@/api/sampling/taskGroup'
import { getSampleRecordsByGroup, getSampleStatisticsByGroup } from '@/api/sampling/sampleRecord'
import SampleManagementH5 from '@/components/h5/sampling/SampleManagementH5.vue'
import PointInfoH5 from '@/components/h5/sampling/PointInfoH5.vue'
import { setCache, getCache, CACHE_KEYS } from '@/utils/cache'
import useUserStore from '@/store/modules/user'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const error = ref('')
const activeTab = ref('task')
const isOnline = ref(navigator.onLine)
const taskData = ref({})
const sampleRecords = ref([])
const sampleStatistics = ref({
  totalCount: 0,
  pendingCount: 0,
  collectedCount: 0,
  submittedCount: 0
})

// 验证用户是否有权限访问该任务
const checkTaskPermission = async (taskId) => {
  try {
    const userId = userStore.id
    console.log('🔍 权限检查开始')
    console.log('当前用户ID:', userId)
    console.log('要访问的分组ID:', taskId)
    console.log('用户store状态:', userStore)

    if (!userId) {
      console.error('❌ 用户ID不存在，请先登录')
      error.value = '用户信息获取失败，请重新登录'
      return false
    }

    // 获取用户有权限的任务列表
    console.log('📡 调用API获取用户分组列表...')
    const response = await getUserGroups(userId)
    console.log('📡 API响应:', response)

    if (response.code === 200) {
      const userTasks = response.data || response.rows || []
      console.log('📋 用户分组列表:', userTasks)
      console.log('📋 分组数量:', userTasks.length)

      // 打印每个分组的详细信息
      userTasks.forEach((task, index) => {
        console.log(`📋 分组${index + 1}:`, {
          id: task.id,
          groupCode: task.groupCode,
          taskName: task.taskName,
          status: task.status
        })
      })

      // 检查当前任务是否在用户的任务列表中
      const hasPermission = userTasks.some(task => {
        const match = task.id == taskId
        console.log(`🔍 检查分组 ${task.id} == ${taskId}:`, match)
        return match
      })

      console.log('🎯 权限检查结果:', hasPermission)

      if (!hasPermission) {
        console.error('❌ 用户无权限访问该任务:', taskId)
        console.error('❌ 用户分组ID列表:', userTasks.map(t => t.id))
        error.value = '您没有权限访问该采样任务'
        return false
      }

      console.log('✅ 权限验证通过')
      return true
    } else {
      console.error('❌ 获取用户任务列表失败:', response)
      error.value = '权限验证失败，请重试'
      return false
    }
  } catch (err) {
    console.error('❌ 权限验证异常:', err)
    error.value = '权限验证失败，请重试'
    return false
  }
}

// 获取任务详情
const fetchTaskDetail = async () => {
  try {
    loading.value = true
    error.value = ''

    const taskId = route.query.id
    if (!taskId) {
      throw new Error('任务ID参数缺失')
    }

    // 首先验证用户权限
    const hasPermission = await checkTaskPermission(taskId)
    if (!hasPermission) {
      loading.value = false
      return
    }

    // 先尝试从缓存获取
    const cacheKey = CACHE_KEYS.TASK_DETAIL + taskId
    const cachedData = getCache(cacheKey)

    if (cachedData) {
      taskData.value = cachedData
      loading.value = false
      await loadSampleData()

      // 后台更新数据
      try {
        const response = await getGroupDetail(taskId)
        if (response.code === 200) {
          taskData.value = response.data
          setCache(cacheKey, response.data)
        }
      } catch (error) {
        console.log('后台更新失败，使用缓存数据')
      }
      return
    }

    // 缓存中没有数据，从服务器获取
    try {
      const response = await getGroupDetail(taskId)
      if (response.code === 200) {
        taskData.value = response.data
        setCache(cacheKey, response.data)
        await loadSampleData()
      } else {
        throw new Error(response.msg || '获取任务详情失败')
      }
    } catch (apiError) {
      // API失败时抛出错误，让上层处理
      throw apiError
    }
  } catch (err) {
    if (err.message && err.message.includes('401')) {
      console.log('认证失败，等待重定向到登录页')
      return
    }
    error.value = err.message || '获取任务详情失败'
    console.error('获取任务详情失败:', err)
  } finally {
    loading.value = false
  }
}

// 加载样品数据
const loadSampleData = async () => {
  if (!taskData.value.id) return

  try {
    const groupId = taskData.value.id

    // 先尝试从缓存获取样品记录
    const recordsCacheKey = CACHE_KEYS.SAMPLE_RECORDS + groupId
    const cachedRecords = getCache(recordsCacheKey)

    if (cachedRecords) {
      sampleRecords.value = cachedRecords
    }

    // 先尝试从缓存获取样品统计
    const statsCacheKey = CACHE_KEYS.SAMPLE_STATISTICS + groupId
    const cachedStats = getCache(statsCacheKey)

    if (cachedStats) {
      sampleStatistics.value = cachedStats
    }

    // 后台更新数据
    try {
      // 加载样品记录
      const recordsResponse = await getSampleRecordsByGroup(groupId)
      if (recordsResponse.code === 200) {
        sampleRecords.value = recordsResponse.data || []
        setCache(recordsCacheKey, sampleRecords.value)
      }

      // 加载样品统计
      const statsResponse = await getSampleStatisticsByGroup(groupId)
      if (statsResponse.code === 200) {
        const stats = statsResponse.data || {
          totalCount: 0,
          pendingCount: 0,
          collectedCount: 0,
          submittedCount: 0
        }
        sampleStatistics.value = stats
        setCache(statsCacheKey, stats)
      }
    } catch (error) {
      console.log('后台更新样品数据失败，使用缓存数据')
      // 如果缓存中也没有数据，保持空状态，让用户知道需要网络连接
    }
  } catch (error) {
    console.error('加载样品数据失败:', error)
  }
}

// 重新加载任务数据
const loadTaskData = () => {
  fetchTaskDetail()
}

// 重试加载
const retryLoad = () => {
  error.value = ''
  fetchTaskDetail()
}

// 跳转到PC端页面
const goToPCVersion = () => {
  const taskId = route.query.id
  const pcUrl = `${window.location.origin}/#/sampling/execution`
  window.open(pcUrl, '_blank')
}

// 返回任务列表
const goToTaskList = () => {
  // 跳转到H5执行任务列表页面
  router.push('/execution-tasks-h5')
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0:
      return 'status-pending'
    case 1:
      return 'status-active'
    case 2:
      return 'status-completed'
    default:
      return ''
  }
}

// 获取状态标签
const getStatusLabel = (status) => {
  switch (status) {
    case 0:
      return '待执行'
    case 1:
      return '执行中'
    case 2:
      return '已完成'
    default:
      return '未知'
  }
}

// 获取样品状态标签
const getSampleStatusLabel = (status) => {
  switch (status) {
    case 0:
      return '待采集'
    case 1:
      return '已采集'
    case 2:
      return '已送检'
    default:
      return '未知'
  }
}

// 网络状态监听
const handleOnline = () => {
  isOnline.value = true
}

const handleOffline = () => {
  isOnline.value = false
}

// 页面加载时获取数据
onMounted(() => {
  fetchTaskDetail()

  // 监听网络状态变化
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style scoped>
.h5-sampling-task {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 20px 15px;
  text-align: center;
}

.header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 500;
}

.task-number, .task-status {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.offline-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  opacity: 0.8;
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.offline-indicator .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.loading, .error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.error .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #f56c6c;
}

.error-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.tab-container {
  background: white;
  margin: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-item {
  flex: 1;
  padding: 12px;
  text-align: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  font-weight: 500;
  background: white;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #409EFF;
}

.tab-content {
  min-height: 400px;
}

.content {
  padding: 0 0 20px;
}

.section {
  margin: 15px 0;
}

.section-title {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.info-grid {
  padding: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  color: #666;
  font-size: 14px;
  min-width: 80px;
  margin-right: 8px;
}

.info-item span {
  color: #333;
  font-size: 14px;
  word-break: break-all;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-active {
  background: #f0f9ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
  background: white;
  margin: 15px;
  border-radius: 8px;
}

.footer p {
  margin: 4px 0;
}

.sample-item {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  background: #fafafa;
}

.sample-item:last-child {
  margin-bottom: 0;
}
</style>
