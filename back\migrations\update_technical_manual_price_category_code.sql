-- 更新技术手册价格表，添加category_code字段并迁移数据
-- 执行时间：2024-01-XX

-- 1. 备份现有数据
CREATE TABLE technical_manual_price_backup AS 
SELECT * FROM technical_manual_price;

-- 2. 添加category_code字段
ALTER TABLE technical_manual_price 
ADD COLUMN category_code VARCHAR(20) COMMENT '类目编号' AFTER method;

-- 3. 迁移数据：根据分类和检测类别查找对应的类目编号
UPDATE technical_manual_price tmp
INNER JOIN technical_manual_category tmc 
ON tmp.classification = tmc.classification 
AND tmp.category = tmc.category
SET tmp.category_code = tmc.category_code
WHERE tmp.category_code IS NULL;

-- 4. 删除del_flag字段（如果存在）
-- 检查字段是否存在
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'technical_manual_price' 
     AND COLUMN_NAME = 'del_flag') > 0,
    'ALTER TABLE technical_manual_price DROP COLUMN del_flag',
    'SELECT "del_flag column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 验证数据迁移
SELECT 
    COUNT(*) as total_records,
    COUNT(category_code) as records_with_category_code,
    COUNT(*) - COUNT(category_code) as records_without_category_code
FROM technical_manual_price;

-- 6. 显示未迁移的记录（如果有）
SELECT id, method, category, classification, category_code
FROM technical_manual_price 
WHERE category_code IS NULL
LIMIT 10;

-- 7. 创建索引优化查询性能
CREATE INDEX idx_technical_manual_price_category_code ON technical_manual_price(category_code);
CREATE INDEX idx_technical_manual_price_method_category_code ON technical_manual_price(method, category_code);

-- 8. 添加外键约束（可选，确保数据完整性）
-- ALTER TABLE technical_manual_price 
-- ADD CONSTRAINT fk_technical_manual_price_category_code 
-- FOREIGN KEY (category_code) REFERENCES technical_manual_category(category_code);

-- 9. 验证最终结果
SELECT 
    tmp.id,
    tmp.method,
    tmp.category_code,
    tmc.classification,
    tmc.category
FROM technical_manual_price tmp
LEFT JOIN technical_manual_category tmc ON tmp.category_code = tmc.category_code
LIMIT 10;


-- 新增字段
ALTER TABLE project_quotation_item_basedata_price
ADD COLUMN `category_code` VARCHAR(20) NOT NULL COMMENT '检测类别的类别编码'
AFTER category;

ALTER TABLE project_quotation_item_basedata_price
MODIFY COLUMN `category` VARCHAR(125) DEFAULT NULL COMMENT '检测类别';

-- 新增字段
ALTER TABLE project_quotation_item
ADD COLUMN `category_code` VARCHAR(20) NOT NULL COMMENT '检测类别的类别编码'
AFTER category;


ALTER TABLE project_quotation_item
MODIFY COLUMN `category` VARCHAR(125) DEFAULT NULL COMMENT '检测类别';

-- 更新值
UPDATE project_quotation_item pqi
JOIN technical_manual_price tmp ON pqi.category = tmp.category
SET pqi.category_code = tmp.category_code;


UPDATE project_quotation_item_basedata_price pqi
JOIN technical_manual_price tmp ON pqi.category = tmp.category
SET pqi.category_code = tmp.category_code;