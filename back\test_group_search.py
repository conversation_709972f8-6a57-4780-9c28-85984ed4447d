"""
搜索分组编号为 25100002-4 的样品信息
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:9099"

# 测试token（跳过认证）
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}


def search_group_by_code(group_code):
    """根据分组编号搜索分组"""
    print("=" * 80)
    print(f"搜索分组编号: {group_code}")
    print("=" * 80)

    # 尝试获取所有分组,然后筛选
    response = requests.get(
        f"{BASE_URL}/sampling/task-group/list",
        headers=HEADERS,
        params={"page_num": 1, "page_size": 1000}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 200:
            # 数据可能是列表或者包含rows的字典
            rows = data.get("data", [])
            if isinstance(rows, dict):
                rows = rows.get("rows", [])

            # 查找匹配的分组
            matching_groups = [g for g in rows if g.get("groupCode") == group_code]

            if matching_groups:
                group = matching_groups[0]
                print(f"\n找到分组:")
                print(f"  分组ID: {group.get('id')}")
                print(f"  分组编号: {group.get('groupCode')}")
                print(f"  分组名称: {group.get('groupName')}")
                print(f"  任务ID: {group.get('taskId')}")
                return group.get('id')
            else:
                print(f"\n未找到分组编号为 {group_code} 的分组")
                print(f"总共有 {len(rows)} 个分组")
                # 打印前5个分组的编号供参考
                if rows:
                    print("\n前5个分组编号:")
                    for g in rows[:5]:
                        print(f"  - {g.get('groupCode')}")
                return None

    print(f"搜索失败: {response.status_code}")
    print(f"错误信息: {response.text}")
    return None


def get_samples_by_group_id(group_id):
    """获取分组的样品记录"""
    print("\n" + "=" * 80)
    print(f"获取分组 {group_id} 的样品记录")
    print("=" * 80)
    
    response = requests.get(
        f"{BASE_URL}/sampling/sample-records/group/{group_id}/with-quality-controls",
        headers=HEADERS
    )
    
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 200:
            samples = data.get("data", [])
            print(f"\n找到 {len(samples)} 条样品记录:\n")
            
            for sample in samples:
                print(f"样品ID: {sample.get('id')}")
                print(f"  样品编号: {sample.get('sampleNumber')}")
                print(f"  样品类型: {sample.get('sampleType')}")
                print(f"  采样来源: {sample.get('sampleSource')}")
                print(f"  点位名称: {sample.get('pointName')}")
                print(f"  检测类别: {sample.get('detectionCategory')}")
                print(f"  检测参数: {sample.get('detectionParameter')}")
                print(f"  是否质控样: {sample.get('isQualityControl')}")
                print(f"  质控样类型: {sample.get('qualityControlType')}")
                print(f"  关联样品ID: {sample.get('relatedSampleId')}")
                print(f"  状态: {sample.get('status')}")
                print("-" * 80)
            
            return samples
    
    print(f"获取样品记录失败: {response.status_code}")
    print(f"错误信息: {response.text}")
    return None


if __name__ == "__main__":
    group_code = "25100002-4"
    
    # 搜索分组
    group_id = search_group_by_code(group_code)
    
    # 如果找到分组,获取样品记录
    if group_id:
        samples = get_samples_by_group_id(group_id)
        
        # 查找样品编号为1的样品
        if samples:
            sample_1 = [s for s in samples if s.get('sampleNumber') == '1']
            if sample_1:
                print("\n" + "=" * 80)
                print("样品编号为 1 的详细信息:")
                print("=" * 80)
                print(json.dumps(sample_1[0], indent=2, ensure_ascii=False))

