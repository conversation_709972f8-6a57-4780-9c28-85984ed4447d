"""
技术手册类目异步服务
"""

import io
import re
from datetime import datetime
from typing import List

import pandas as pd
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryModel,
    TechnicalManualCategoryQueryModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageUtil


class TechnicalManualCategoryService:
    """
    技术手册类目异步服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_category_list(
        self, query_object: TechnicalManualCategoryQueryModel
    ) -> List[TechnicalManualCategoryModel]:
        """
        获取技术手册类目列表

        :param query_object: 查询参数对象
        :return: 技术手册类目列表
        """
        # 构建查询条件
        conditions = []

        if query_object.classification:
            conditions.append(TechnicalManualCategory.classification.like(f"%{query_object.classification}%"))
        if query_object.classification_code:
            conditions.append(TechnicalManualCategory.classification_code == query_object.classification_code)
        if query_object.category:
            conditions.append(TechnicalManualCategory.category.like(f"%{query_object.category}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualCategory.category_code == query_object.category_code)
        if query_object.status:
            conditions.append(TechnicalManualCategory.status == query_object.status)

        # 执行查询
        stmt = (
            select(TechnicalManualCategory)
            .where(and_(*conditions))
            .order_by(TechnicalManualCategory.classification, TechnicalManualCategory.category)
        )
        result = await self.db.execute(stmt)
        category_list = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category_list)

    async def get_category_page(self, query_object: TechnicalManualCategoryQueryModel):
        """
        获取技术手册类目分页列表

        :param query_object: 查询参数对象
        :return: 技术手册类目分页列表
        """
        # 构建查询条件
        conditions = []

        if query_object.classification:
            conditions.append(TechnicalManualCategory.classification.like(f"%{query_object.classification}%"))
        if query_object.classification_code:
            conditions.append(TechnicalManualCategory.classification_code == query_object.classification_code)
        if query_object.category:
            conditions.append(TechnicalManualCategory.category.like(f"%{query_object.category}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualCategory.category_code == query_object.category_code)
        if query_object.status:
            conditions.append(TechnicalManualCategory.status == query_object.status)

        # 执行查询
        stmt = (
            select(TechnicalManualCategory)
            .where(and_(*conditions))
            .order_by(
                TechnicalManualCategory.create_time.desc(),
                TechnicalManualCategory.classification,
                TechnicalManualCategory.category,
            )
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_category_detail(self, id: int) -> TechnicalManualCategoryModel:
        """
        获取技术手册类目详情

        :param id: 技术手册类目ID
        :return: 技术手册类目详情
        """
        stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == id)
        result = await self.db.execute(stmt)
        category = result.scalars().first()

        if not category:
            raise ServiceException(message=f"技术手册类目ID：{id}不存在")

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category)

    async def add_category(self, category_model: TechnicalManualCategoryModel, current_user: CurrentUserModel):
        """
        新增技术手册类目

        :param category_model: 新增技术手册类目对象
        :param current_user: 当前用户对象
        :return: 新增结果
        """
        try:
            # 校验分类和检测类别组合是否唯一
            if not await self.check_category_unique(category_model.classification, category_model.category):
                raise ServiceException(message=f"新增技术手册类目失败，该分类和检测类别组合已存在")

            # 生成类目编号
            category_code = await self.generate_category_code()

            # 生成分类编码
            classification_code = self.generate_classification_code(category_model.classification)

            # 创建技术手册类目对象
            category = TechnicalManualCategory(
                category_code=category_code,
                classification=category_model.classification,
                classification_code=classification_code,
                category=category_model.category,
                status=category_model.status or "0",
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=category_model.remark,
            )

            # 新增技术手册类目
            self.db.add(category)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="新增成功", result={"id": category.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def edit_category(self, category_model: TechnicalManualCategoryModel, current_user: CurrentUserModel):
        """
        编辑技术手册类目

        :param category_model: 编辑技术手册类目对象
        :param current_user: 当前用户对象
        :return: 编辑结果
        """
        try:
            # 获取技术手册类目对象
            stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == category_model.id)
            result = await self.db.execute(stmt)
            category = result.scalars().first()
            if not category:
                raise ServiceException(message=f"技术手册类目ID：{category_model.id}不存在")

            # 校验分类和检测类别组合是否唯一
            if not await self.check_category_unique(
                category_model.classification, category_model.category, category_model.id
            ):
                raise ServiceException(message=f"修改技术手册类目失败，该分类和检测类别组合已存在")

            # 更新技术手册类目对象
            category.classification = category_model.classification
            category.classification_code = self.generate_classification_code(category_model.classification, category_model.category)
            category.category = category_model.category
            category.status = category_model.status
            category.update_by = current_user.user.user_name if current_user and current_user.user else ""
            category.update_time = datetime.now()
            category.remark = category_model.remark

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="修改成功", result={"id": category.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def delete_category(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册类目

        :param id: 技术手册类目ID
        :param current_user: 当前用户对象
        :return: 删除结果
        """
        try:
            # 检查技术手册类目是否存在
            stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == id)
            result = await self.db.execute(stmt)
            category = result.scalars().first()
            if not category:
                raise ServiceException(message=f"技术手册类目ID：{id}不存在")

            # 真删除技术手册类目
            await self.db.delete(category)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="删除成功", result={"id": id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def check_category_unique(self, classification: str, category: str, id: int = None):
        """
        检查技术手册类目是否唯一

        :param classification: 分类
        :param category: 检测类别
        :param id: 技术手册类目ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.category == category,
        ]

        if id:
            conditions.append(TechnicalManualCategory.id != id)

        stmt = select(TechnicalManualCategory).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def generate_category_code(self) -> str:
        """
        生成类目编号

        :return: 类目编号
        """
        # 查询最大的类目编号
        stmt = select(func.max(TechnicalManualCategory.category_code)).where(
            TechnicalManualCategory.category_code.like("CATE%")
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        if max_code:
            # 提取数字部分并加1
            num = int(max_code[4:]) + 1
        else:
            num = 1

        return f"CATE{num:05d}"

    def generate_classification_code(self, classification: str, category: str = None) -> str:
        """
        生成分类编码和类别获取分类编码
        根据分类中文名称生成编码，取中文首字母大写

        :param classification: 分类名称
        :return: 分类编码
        """
        if not classification:
            return ""

        # 常见分类名称映射表（更新版）
        classification_mapping = {
            # 气体类 - Q
            "公共场所": "Q",
            "气": "Q",
            "油气回收": "Q",
            
            # 固体类 - G  
            "固": "G",
            "土": "G",
            "水处理剂": "G",
            "水泥胶砂": "G", 
            "污泥": "G",
            
            # 水体类 - S
            "水": "S",
            
            # 声学类 - Z
            "声": "Z",
            "振动": "Z",
            
            # 生态类 - B
            "生态": "B",
        }

        # 特殊条件映射（需要额外判断的分类）
        special_classification_mapping = {
            "海洋": {
                "condition_field": "category",
                "mappings": {
                    "海水": "S",      # 当 category == '海水' 时
                    "default": "G"    # 当 category != '海水' 或为空时
                }
            }
        }


        # 优先检查简单映射
        if classification in classification_mapping:
            return classification_mapping[classification]
        
        # 检查特殊条件映射
        if classification in special_classification_mapping:
            special_rule = special_classification_mapping[classification]
            condition_field_value = category  # 这里假设是 category 字段
            if classification == "海洋":
                if condition_field_value == "海水":
                    return "S"
                else:
                    return "G"
        

        # 如果映射表中没有，取首字符
        first_char = classification[0]

        # 如果是中文字符，使用简单的拼音映射
        chinese_pinyin_map = {
            "公": "G", "固": "G", "气": "Q", "水": "S", "土": "T", "噪": "Z",
            "废": "F", "环": "H", "食": "S", "建": "J", "化": "H", "生": "S",
            "物": "W", "辐": "F", "电": "D", "振": "Z", "光": "G", "温": "W",
            "湿": "S", "压": "Y", "检": "J", "测": "C", "分": "F", "析": "X",
            "实": "S", "验": "Y", "室": "S", "质": "Z", "量": "L", "控": "K",
            "制": "Z", "管": "G", "理": "L", "系": "X", "统": "T", "技": "J",
            "术": "S", "标": "B", "准": "Z", "规": "G", "范": "F", "方": "F",
            "法": "F", "程": "C", "序": "X", "流": "L", "程": "C", "操": "C",
            "作": "Z", "指": "Z", "导": "D", "手": "S", "册": "C", "文": "W",
            "档": "D", "记": "J", "录": "L", "报": "B", "告": "G", "结": "J",
            "果": "G", "数": "S", "据": "J", "信": "X", "息": "X", "资": "Z",
            "料": "L", "样": "Y", "品": "P", "试": "S", "剂": "J", "设": "S",
            "备": "B", "仪": "Y", "器": "Q", "工": "G", "具": "J", "材": "C",
            "料": "L", "耗": "H", "材": "C"
        }

        if first_char in chinese_pinyin_map:
            return chinese_pinyin_map[first_char]

        # 如果都没有匹配，返回首字符的大写（适用于英文等）
        return first_char.upper()

    async def get_classifications(self) -> List[str]:
        """
        获取所有分类

        :return: 分类列表
        """
        stmt = (
            select(TechnicalManualCategory.classification).distinct().order_by(TechnicalManualCategory.classification)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_categories_by_classification(self, classification: str) -> List[str]:
        """
        根据分类获取检测类别

        :param classification: 分类
        :return: 检测类别列表
        """
        stmt = (
            select(TechnicalManualCategory.category)
            .where(TechnicalManualCategory.classification == classification)
            .distinct()
            .order_by(TechnicalManualCategory.category)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_categories(self) -> List[str]:
        """
        获取所有检测类别

        :return: 检测类别列表
        """
        stmt = select(TechnicalManualCategory.category).distinct().order_by(TechnicalManualCategory.category)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_category_tree(self):
        """
        获取类目树形数据

        :return: 类目树形数据
        """
        # 获取所有类目数据
        stmt = select(TechnicalManualCategory).order_by(
            TechnicalManualCategory.classification, TechnicalManualCategory.category
        )
        result = await self.db.execute(stmt)
        categories = result.scalars().all()

        # 构建树形结构
        tree = {}
        for category in categories:
            if category.classification not in tree:
                tree[category.classification] = {
                    "label": category.classification,
                    "value": category.classification,
                    "children": [],
                }

            tree[category.classification]["children"].append(
                {
                    "label": category.category,
                    "value": category.category_code,
                    "categoryCode": category.category_code,
                    "classification": category.classification,
                    "category": category.category,
                }
            )

        return list(tree.values())

    async def export_categories(self, query_object: TechnicalManualCategoryQueryModel) -> bytes:
        """
        导出技术手册类目

        :param query_object: 查询参数对象
        :return: Excel文件字节数据
        """
        # 获取数据
        categories = await self.get_category_list(query_object)

        # 转换为DataFrame
        data = []
        for category in categories:
            data.append(
                {
                    "类目编号": category.get("categoryCode"),
                    "分类": category.get("classification"),
                    "分类编码": category.get("classificationCode"),
                    "检测类别": category.get("category"),
                    "状态": "正常" if category.get("status") == "0" else "停用",
                    "创建时间": category.get("createTime"),
                    "备注": category.get("remark") or "",
                }
            )

        df = pd.DataFrame(data)

        # 保存到字节流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="技术手册类目", index=False)

        output.seek(0)
        return output.getvalue()
