<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由参数自动填充功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409EFF;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-section h2 {
            color: #303133;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .test-link {
            display: block;
            padding: 12px 20px;
            background: #409EFF;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background: #337ecc;
        }
        .description {
            background: #f0f9ff;
            padding: 15px;
            border-left: 4px solid #409EFF;
            margin-top: 15px;
        }
        .code-block {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .success {
            color: #67C23A;
            font-weight: bold;
        }
        .warning {
            color: #E6A23C;
            font-weight: bold;
        }
        .info {
            color: #909399;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 路由参数自动填充功能测试</h1>
        
        <div class="test-section">
            <h2>📋 项目报价页面测试</h2>
            <div class="test-links">
                <a href="/#/quotation/project-quotation?projectName=某商场空气质量检测项目" class="test-link">
                    测试项目名称自动填充
                </a>
                <a href="/#/quotation/project-quotation?projectCode=PROJ2024001" class="test-link">
                    测试项目编号自动填充
                </a>
                <a href="/#/quotation/project-quotation?customerName=某商场管理有限公司" class="test-link">
                    测试客户名称自动填充
                </a>
                <a href="/#/quotation/project-quotation?projectName=水质检测&projectCode=PROJ2024002" class="test-link">
                    测试多参数自动填充
                </a>
            </div>
            <div class="description">
                <strong>测试说明：</strong>
                <ul>
                    <li>点击上方链接将跳转到项目报价页面</li>
                    <li>页面应自动填充对应的查询条件</li>
                    <li>查询条件应显示在搜索表单中</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 采样任务页面测试</h2>
            <div class="test-links">
                <a href="/#/sampling/task?taskName=公共场所空气质量检测任务001" class="test-link">
                    测试任务名称自动填充
                </a>
                <a href="/#/sampling/task?taskCode=TASK2024001" class="test-link">
                    测试任务编号自动填充
                </a>
                <a href="/#/sampling/task?status=2" class="test-link">
                    测试任务状态自动填充
                </a>
                <a href="/#/sampling/task?taskName=水质检测&status=1" class="test-link">
                    测试多参数自动填充
                </a>
            </div>
            <div class="description">
                <strong>测试说明：</strong>
                <ul>
                    <li>点击上方链接将跳转到采样任务页面</li>
                    <li>页面应自动填充对应的查询条件</li>
                    <li>查询条件应显示在搜索表单中</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 技术实现详情</h2>
            <div class="description">
                <h3>项目报价页面 (Vue 3 Composition API)</h3>
                <div class="code-block">
// 导入路由相关函数
import { useRouter, useRoute } from 'vue-router'

const route = useRoute()

// 在 onMounted 中处理路由参数
onMounted(() => {
  if (route.query.projectName) {
    queryParams.value.projectName = route.query.projectName
  }
  if (route.query.projectCode) {
    queryParams.value.projectCode = route.query.projectCode
  }
  if (route.query.customerName) {
    queryParams.value.customerName = route.query.customerName
  }
  
  getList()
})
                </div>

                <h3>采样任务页面 (Vue 2 Options API)</h3>
                <div class="code-block">
// 在 created 生命周期中处理路由参数
created() {
  if (this.$route.query.taskName) {
    this.queryParams.taskName = this.$route.query.taskName;
  }
  if (this.$route.query.taskCode) {
    this.queryParams.taskCode = this.$route.query.taskCode;
  }
  if (this.$route.query.status) {
    this.queryParams.status = this.$route.query.status;
  }
  
  this.getDictData();
  this.getList();
  this.getUserList();
  this.getQuotationList();
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ 验证步骤</h2>
            <div class="description">
                <ol>
                    <li><span class="success">✓</span> 点击测试链接跳转到对应页面</li>
                    <li><span class="success">✓</span> 检查搜索表单是否自动填充了查询条件</li>
                    <li><span class="success">✓</span> 验证数据列表是否根据查询条件进行了过滤</li>
                    <li><span class="success">✓</span> 测试清空查询条件后重新搜索</li>
                    <li><span class="success">✓</span> 测试修改查询条件后重新搜索</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 履约情况跳转测试</h2>
            <div class="description">
                <p><strong>从合同履约情况页面跳转测试：</strong></p>
                <ol>
                    <li>进入合同详情页面</li>
                    <li>切换到"履约情况"标签页</li>
                    <li>点击任务名称链接 → 应跳转到采样任务页面并自动填充任务名称</li>
                    <li>点击项目名称链接 → 应跳转到项目报价页面并自动填充项目名称</li>
                    <li>点击报价单编号链接 → 应跳转到项目报价页面并自动填充项目编号</li>
                </ol>
                <p class="info">💡 提示：跳转成功后会显示成功消息，跳转失败会显示错误消息</p>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 测试结果记录</h2>
            <div class="description">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f5f7fa;">
                            <th style="border: 1px solid #ddd; padding: 8px;">测试项目</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">预期结果</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">实际结果</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">项目名称自动填充</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">搜索框显示传入的项目名称</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">待测试</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">⏳</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">项目编号自动填充</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">搜索框显示传入的项目编号</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">待测试</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">⏳</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">任务名称自动填充</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">搜索框显示传入的任务名称</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">待测试</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">⏳</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">履约情况跳转</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">点击链接正确跳转并填充参数</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">待测试</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">⏳</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
