"""
测试质控样编号生成逻辑
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.dto.sample_record_dto import QualityControlSampleCreateDTO
from utils.quality_control_identifier_util import QualityControlIdentifierUtil


class TestQualityControlSampleNumber:
    """测试质控样编号生成"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def sample_record_service(self, mock_db):
        """创建样品记录服务实例"""
        service = SampleRecordService(mock_db)
        return service
    
    @pytest.fixture
    def original_sample(self):
        """创建原样品记录"""
        sample = MagicMock(spec=SampleRecord)
        sample.id = 1
        sample.sample_number = "2501001S001"  # 原样品编号
        sample.sampling_task_group_id = 1
        sample.sample_type = "水"
        sample.sample_source = "地表水"
        sample.point_name = "测试点位1"
        sample.cycle_number = 1
        sample.cycle_type = "月度"
        sample.detection_category = "地表水"
        sample.detection_parameter = "pH"
        sample.detection_method = "玻璃电极法"
        return sample
    
    @pytest.mark.asyncio
    async def test_create_quality_control_sample_with_parallel_sample(self, sample_record_service, original_sample):
        """测试创建平行样"""
        # 模拟数据库查询
        sample_record_service.sample_record_dao.get_sample_record_by_id = AsyncMock(return_value=original_sample)
        sample_record_service.sample_record_dao.batch_create_sample_records = AsyncMock(
            return_value=[MagicMock(
                id=2,
                sample_number="2501001S001TP",
                is_quality_control=True,
                quality_control_type="parallel_sample",
                related_sample_id=1,
                sampling_task_group_id=1,
                sample_type="水",
                sample_source="地表水",
                point_name="测试点位1",
                cycle_number=1,
                cycle_type="月度",
                detection_category="地表水",
                detection_parameter="pH",
                detection_method="玻璃电极法",
                status=0,
                collection_time=None,
                submission_time=None,
                completion_time=None,
                remark=None,
                create_by=1,
                create_time=None,
                update_by=1,
                update_time=None
            )]
        )
        sample_record_service.db.commit = AsyncMock()
        
        # 创建质控样DTO
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=1,
            quality_control_types=["parallel_sample"]
        )
        
        # 执行创建
        results = await sample_record_service.create_quality_control_samples(create_dto, create_by=1)
        
        # 验证结果
        assert len(results) == 1
        assert results[0].sample_number == "2501001S001TP"
        assert results[0].is_quality_control == True
        assert results[0].quality_control_type == "parallel_sample"
        assert results[0].related_sample_id == 1
    
    @pytest.mark.asyncio
    async def test_create_multiple_quality_control_samples(self, sample_record_service, original_sample):
        """测试创建多个质控样"""
        # 模拟数据库查询
        sample_record_service.sample_record_dao.get_sample_record_by_id = AsyncMock(return_value=original_sample)
        
        created_samples = [
            MagicMock(
                id=2,
                sample_number="2501001S001TP",
                is_quality_control=True,
                quality_control_type="parallel_sample",
                related_sample_id=1,
                sampling_task_group_id=1,
                sample_type="水",
                sample_source="地表水",
                point_name="测试点位1",
                cycle_number=1,
                cycle_type="月度",
                detection_category="地表水",
                detection_parameter="pH",
                detection_method="玻璃电极法",
                status=0,
                collection_time=None,
                submission_time=None,
                completion_time=None,
                remark=None,
                create_by=1,
                create_time=None,
                update_by=1,
                update_time=None
            ),
            MagicMock(
                id=3,
                sample_number="2501001S001TQ",
                is_quality_control=True,
                quality_control_type="full_blank_sample",
                related_sample_id=1,
                sampling_task_group_id=1,
                sample_type="水",
                sample_source="地表水",
                point_name="测试点位1",
                cycle_number=1,
                cycle_type="月度",
                detection_category="地表水",
                detection_parameter="pH",
                detection_method="玻璃电极法",
                status=0,
                collection_time=None,
                submission_time=None,
                completion_time=None,
                remark=None,
                create_by=1,
                create_time=None,
                update_by=1,
                update_time=None
            )
        ]
        
        sample_record_service.sample_record_dao.batch_create_sample_records = AsyncMock(return_value=created_samples)
        sample_record_service.db.commit = AsyncMock()
        
        # 创建质控样DTO
        create_dto = QualityControlSampleCreateDTO(
            original_sample_id=1,
            quality_control_types=["parallel_sample", "full_blank_sample"]
        )
        
        # 执行创建
        results = await sample_record_service.create_quality_control_samples(create_dto, create_by=1)
        
        # 验证结果
        assert len(results) == 2
        assert results[0].sample_number == "2501001S001TP"
        assert results[0].quality_control_type == "parallel_sample"
        assert results[1].sample_number == "2501001S001TQ"
        assert results[1].quality_control_type == "full_blank_sample"
    
    def test_quality_control_identifier_util_parallel_sample(self):
        """测试质控标识工具 - 平行样"""
        identifier = QualityControlIdentifierUtil.get_identifier("parallel_sample")
        assert identifier == "TP"
    
    def test_quality_control_identifier_util_full_blank_sample(self):
        """测试质控标识工具 - 全程空白样"""
        identifier = QualityControlIdentifierUtil.get_identifier("full_blank_sample")
        assert identifier == "TQ"
    
    def test_quality_control_identifier_util_transport_blank_sample(self):
        """测试质控标识工具 - 运输空白样"""
        identifier = QualityControlIdentifierUtil.get_identifier("transport_blank_sample")
        assert identifier == "TY"
    
    def test_quality_control_identifier_util_equipment_blank_sample(self):
        """测试质控标识工具 - 设备清洗空白样"""
        identifier = QualityControlIdentifierUtil.get_identifier("equipment_blank_sample")
        assert identifier == "TS"
    
    def test_quality_control_identifier_util_matrix_spike_sample(self):
        """测试质控标识工具 - 基体加标样"""
        identifier = QualityControlIdentifierUtil.get_identifier("matrix_spike_sample")
        assert identifier == "TJ"
    
    def test_quality_control_identifier_util_lab_parallel_sample(self):
        """测试质控标识工具 - 实验室平行样"""
        identifier = QualityControlIdentifierUtil.get_identifier("lab_parallel_sample")
        assert identifier == "TSP"
    
    def test_quality_control_identifier_util_unknown(self):
        """测试质控标识工具 - 未知类型"""
        identifier = QualityControlIdentifierUtil.get_identifier("unknown_type")
        assert identifier == ""
    
    def test_quality_control_identifier_util_empty(self):
        """测试质控标识工具 - 空字符串"""
        identifier = QualityControlIdentifierUtil.get_identifier("")
        assert identifier == ""
    
    def test_quality_control_identifier_util_none(self):
        """测试质控标识工具 - None"""
        identifier = QualityControlIdentifierUtil.get_identifier(None)
        assert identifier == ""
    
    def test_is_valid_qc_type(self):
        """测试质控样类型有效性检查"""
        assert QualityControlIdentifierUtil.is_valid_qc_type("parallel_sample") == True
        assert QualityControlIdentifierUtil.is_valid_qc_type("full_blank_sample") == True
        assert QualityControlIdentifierUtil.is_valid_qc_type("unknown_type") == False
        assert QualityControlIdentifierUtil.is_valid_qc_type("") == False
        assert QualityControlIdentifierUtil.is_valid_qc_type(None) == False

