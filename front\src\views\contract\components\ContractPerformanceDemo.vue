<template>
  <div class="contract-performance-demo">
    <h3>合同履约情况演示</h3>
    
    <!-- 过滤条件 -->
    <el-form :model="queryParams" :inline="true" label-width="80px">
      <el-form-item label="项目编号">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="项目名称">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="任务名称">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 任务列表 -->
    <el-table
      v-loading="loading"
      :data="filteredTaskList"
      style="width: 100%"
      size="small"
      border
    >
      <el-table-column prop="taskName" label="任务名称" min-width="150">
        <template #default="scope">
          <el-link type="primary" @click="goToSamplingTask(scope.row.taskName)">
            {{ scope.row.taskName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="项目名称" min-width="200">
        <template #default="scope">
          <el-link type="primary" @click="goToProjectQuotation('projectName', scope.row.projectName)">
            {{ scope.row.projectName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="projectCode" label="报价单编号" min-width="150">
        <template #default="scope">
          <el-link type="primary" @click="goToProjectQuotation('projectCode', scope.row.projectCode)">
            {{ scope.row.projectCode }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户名称" min-width="200" />
      <el-table-column prop="responsibleUserName" label="负责人" width="100" />
      <el-table-column prop="statusText" label="任务状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="plannedStartDate" label="计划开始日期" width="120" />
      <el-table-column prop="plannedEndDate" label="计划结束日期" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="150" />
    </el-table>

    <!-- 功能说明 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>功能说明</span>
      </template>
      <div>
        <p><strong>履约情况功能特点：</strong></p>
        <ul>
          <li>✅ 自动获取合同关联的所有报价单</li>
          <li>✅ 展示每个报价单对应的采样任务</li>
          <li>✅ 支持按项目编号、项目名称、任务名称过滤</li>
          <li>✅ 点击任务名称跳转到采样任务页面并自动填充查询条件</li>
          <li>✅ 点击项目名称或报价单编号跳转到项目报价页面并自动填充查询条件</li>
          <li>✅ 实时显示任务状态和进度</li>
        </ul>
        <p><strong>跳转功能：</strong></p>
        <ul>
          <li>任务名称 → 采样任务页面（自动填充任务名称查询）</li>
          <li>项目名称 → 项目报价页面（自动填充项目名称查询）</li>
          <li>报价单编号 → 项目报价页面（自动填充项目编号查询）</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

const props = defineProps({
  contractId: {
    type: Number,
    default: 1
  }
})

// 数据
const loading = ref(false)
const queryParams = ref({
  projectCode: '',
  projectName: '',
  taskName: ''
})

// 模拟数据
const mockTaskList = ref([
  {
    id: 1,
    taskName: '公共场所空气质量检测任务001',
    projectName: '某商场空气质量检测项目',
    projectCode: 'PROJ2024001',
    customerName: '某商场管理有限公司',
    responsibleUserName: '张三',
    status: 2,
    statusText: '执行中',
    plannedStartDate: '2024-01-15',
    plannedEndDate: '2024-01-25',
    createTime: '2024-01-10 09:00:00'
  },
  {
    id: 2,
    taskName: '水质检测任务002',
    projectName: '某工厂废水检测项目',
    projectCode: 'PROJ2024002',
    customerName: '某制造企业有限公司',
    responsibleUserName: '李四',
    status: 1,
    statusText: '已分配',
    plannedStartDate: '2024-01-20',
    plannedEndDate: '2024-01-30',
    createTime: '2024-01-12 14:30:00'
  },
  {
    id: 3,
    taskName: '土壤检测任务003',
    projectName: '某地块土壤污染检测项目',
    projectCode: 'PROJ2024003',
    customerName: '某房地产开发公司',
    responsibleUserName: '王五',
    status: 3,
    statusText: '已完成',
    plannedStartDate: '2024-01-05',
    plannedEndDate: '2024-01-15',
    createTime: '2024-01-01 10:15:00'
  }
])

// 过滤后的任务列表
const filteredTaskList = computed(() => {
  let filtered = mockTaskList.value
  
  if (queryParams.value.taskName) {
    filtered = filtered.filter(task => 
      task.taskName.includes(queryParams.value.taskName)
    )
  }
  
  if (queryParams.value.projectName) {
    filtered = filtered.filter(task => 
      task.projectName.includes(queryParams.value.projectName)
    )
  }
  
  if (queryParams.value.projectCode) {
    filtered = filtered.filter(task => 
      task.projectCode.includes(queryParams.value.projectCode)
    )
  }
  
  return filtered
})

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning', 
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return typeMap[status] || 'info'
}

// 跳转到采样任务页面
const goToSamplingTask = (taskName) => {
  ElMessage.success(`将跳转到采样任务页面，查询条件：任务名称="${taskName}"`)
  // 实际跳转代码：
  // router.push({
  //   path: '/sampling/task',
  //   query: { taskName: taskName }
  // })
}

// 跳转到项目报价页面
const goToProjectQuotation = (type, value) => {
  const queryType = type === 'projectName' ? '项目名称' : '项目编号'
  ElMessage.success(`将跳转到项目报价页面，查询条件：${queryType}="${value}"`)
  // 实际跳转代码：
  // const query = {}
  // if (type === 'projectName') {
  //   query.projectName = value
  // } else if (type === 'projectCode') {
  //   query.projectCode = value
  // }
  // router.push({
  //   path: '/quotation/project-quotation',
  //   query: query
  // })
}

// 搜索
const handleQuery = () => {
  ElMessage.info('执行搜索操作')
}

// 重置
const resetQuery = () => {
  queryParams.value = {
    projectCode: '',
    projectName: '',
    taskName: ''
  }
  ElMessage.info('重置查询条件')
}

onMounted(() => {
  ElMessage.success(`加载合同ID ${props.contractId} 的履约情况数据`)
})
</script>

<style scoped>
.contract-performance-demo {
  padding: 20px;
}

.el-card {
  margin-top: 20px;
}

.el-card ul {
  margin: 10px 0;
  padding-left: 20px;
}

.el-card li {
  margin: 5px 0;
}
</style>
