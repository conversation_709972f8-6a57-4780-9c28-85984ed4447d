-- 修改样品记录表sample_number字段类型
-- 创建时间: 2025-01-27
-- 描述: 将sample_record表的sample_number字段从int类型修改为varchar类型，以支持新的样品编号格式：任务单号+类别标识+样品序号

-- 1. 首先备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS sample_record_backup_20250127 AS 
SELECT * FROM sample_record;

-- 2. 修改字段类型为varchar
ALTER TABLE sample_record 
MODIFY COLUMN sample_number VARCHAR(100) NOT NULL COMMENT '样品编号（格式：任务单号+类别标识+样品序号）';

-- 3. 验证数据迁移
-- 可以通过以下查询验证迁移是否成功：
-- SELECT sample_number, COUNT(*) FROM sample_record GROUP BY sample_number;

