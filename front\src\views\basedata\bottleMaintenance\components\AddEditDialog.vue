<template>
  <el-dialog
    :title="isEdit ? '编辑瓶组管理' : '新增瓶组管理'"
    v-model="dialogVisible"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      style="padding-right: 20px"
    >
      <!-- 基础信息 -->
      <el-divider content-position="left">基础信息</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="瓶组编码" prop="bottleCode">
            <el-input v-model="form.bottleCode" disabled placeholder="系统自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="容器类型" prop="bottleType">
            <el-input v-model="form.bottleType" placeholder="请输入容器类型" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="容器容量" prop="bottleVolume">
            <el-input v-model="form.bottleVolume" placeholder="如：100ML, 1kg, >1L" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品时效单位" prop="sampleAgeUnit">
            <el-select v-model="form.sampleAgeUnit" placeholder="请选择时效单位" style="width: 100%">
              <el-option label="小时" value="小时" />
              <el-option label="天" value="天" />
              <el-option label="周" value="周" />
              <el-option label="月" value="月" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品时效" prop="sampleAge">
            <el-input-number
              v-model="form.sampleAge"
              :min="1"
              :max="999"
              placeholder="请输入时效数值"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="存储方式" prop="storageStyles">
            <el-select
              v-model="form.storageStyles"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入存储方式"
              style="width: 100%"
            >
              <el-option label="冷藏" value="冷藏" />
              <el-option label="避光" value="避光" />
              <el-option label="常温" value="常温" />
              <el-option label="干燥" value="干燥" />
              <el-option label="密封" value="密封" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="固定方式" prop="fixStyles">
            <el-select
              v-model="form.fixStyles"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入固定方式"
              style="width: 100%"
            >
              <el-option label="尽快安装" value="尽快安装" />
              <el-option label="吊顶上" value="吊顶上" />
              <el-option label="墙面固定" value="墙面固定" />
              <el-option label="地面放置" value="地面放置" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 技术手册关联 -->
      <el-divider content-position="left">技术手册关联</el-divider>
      
      <el-form-item label="关联技术手册">
        <el-button type="primary" @click="showTechnicalManualDialog">
          ➕ 添加技术手册
        </el-button>
        <span style="margin-left: 10px; color: #909399; font-size: 12px">
          已选择 {{ selectedTechnicalManuals.length }} 项
        </span>
      </el-form-item>

      <!-- 已选择的技术手册列表 -->
      <el-table
        v-if="selectedTechnicalManuals.length > 0"
        :data="selectedTechnicalManuals"
        border
        size="small"
        style="margin-top: 10px"
      >
        <el-table-column prop="category" label="检测类别" width="150" />
        <el-table-column prop="parameter" label="检测参数" width="200" />
        <el-table-column prop="method" label="检测方法" />
        <el-table-column label="操作" width="80">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeTechnicalManual($index)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>

    <!-- 技术手册选择对话框 -->
    <TechnicalManualSelectDialog
      :visible="technicalManualDialogVisible"
      :selected-ids="selectedTechnicalManualIds"
      @update:visible="technicalManualDialogVisible = $event"
      @confirm="handleTechnicalManualSelect"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { addBottleMaintenance, editBottleMaintenance } from '@/api/basedata/bottleMaintenance'
import TechnicalManualSelectDialog from '@/components/technicalManual/TechnicalManualSelectDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)
const technicalManualDialogVisible = ref(false)
const selectedTechnicalManuals = ref([])

// 表单数据
const form = reactive({
  id: undefined,
  bottleCode: '',
  bottleType: '',
  bottleVolume: '',
  storageStyles: [],
  fixStyles: [],
  sampleAge: undefined,
  sampleAgeUnit: '',
  remark: '',
  technicalManualIds: []
})

// 表单验证规则
const rules = {
  bottleType: [
    { required: true, message: '请输入容器类型', trigger: 'blur' }
  ],
  bottleVolume: [
    { required: true, message: '请输入容器容量', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const selectedTechnicalManualIds = computed(() => {
  return selectedTechnicalManuals.value.map(item => item.id)
})

// 监听表单数据变化
watch(() => props.formData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      id: newData.id,
      bottleCode: newData.bottleCode || '',
      bottleType: newData.bottleType || '',
      bottleVolume: newData.bottleVolume || '',
      storageStyles: newData.storageStyles || [],
      fixStyles: newData.fixStyles || [],
      sampleAge: newData.sampleAge,
      sampleAgeUnit: newData.sampleAgeUnit || '',
      remark: newData.remark || ''
    })
    selectedTechnicalManuals.value = newData.technicalManuals || []
  }
}, { immediate: true, deep: true })

// 方法
const resetForm = () => {
  Object.assign(form, {
    id: undefined,
    bottleCode: '',
    bottleType: '',
    bottleVolume: '',
    storageStyles: [],
    fixStyles: [],
    sampleAge: undefined,
    sampleAgeUnit: '',
    remark: '',
    technicalManualIds: []
  })
  selectedTechnicalManuals.value = []
  formRef.value?.resetFields()
}

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

const showTechnicalManualDialog = () => {
  technicalManualDialogVisible.value = true
}

const handleTechnicalManualSelect = (selectedManuals) => {
   //去重
  for (const manual of selectedManuals) {
    if (selectedTechnicalManuals.value.findIndex(t => t.id === manual.id) === -1) {
      selectedTechnicalManuals.value.push(manual)
    }
  }
  technicalManualDialogVisible.value = false
}

const removeTechnicalManual = (index) => {
  selectedTechnicalManuals.value.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 准备提交数据
    const submitData = {
      ...form,
      technicalManualIds: selectedTechnicalManualIds.value
    }
    
    if (props.isEdit) {
      await editBottleMaintenance(submitData)
      ElMessage.success('编辑成功')
    } else {
      await addBottleMaintenance(submitData)
      ElMessage.success('新增成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    ElMessage.error(props.isEdit ? '编辑失败' : '新增失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-divider__text) {
  font-weight: bold;
  color: #409eff;
}
</style>
