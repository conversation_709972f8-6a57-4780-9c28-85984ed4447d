"""
测试质控样瓶组复制功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.dto.sample_record_dto import QualityControlSampleCreateDTO


async def test_quality_control_bottle_groups():
    """测试质控样瓶组复制"""
    print("=" * 80)
    print("测试质控样瓶组复制功能")
    print("=" * 80)
    
    async for db in get_db():
        try:
            # 1. 查找一个有瓶组关联的样品
            print("\n1. 查找有瓶组关联的样品...")
            bottle_group_service = SamplingBottleGroupService(db)
            
            # 尝试查找样品ID=1的瓶组
            original_sample_id = 1
            original_bottle_groups = await bottle_group_service.get_bottle_groups_by_sample(original_sample_id)
            
            if not original_bottle_groups:
                print(f"   ❌ 样品 {original_sample_id} 没有关联的瓶组")
                print("   提示：请先为样品生成瓶组，或者使用其他有瓶组的样品进行测试")
                return
            
            print(f"   ✅ 样品 {original_sample_id} 关联了 {len(original_bottle_groups)} 个瓶组:")
            for bg in original_bottle_groups:
                print(f"      - 瓶组编号: {bg.bottle_group_code}, 瓶组ID: {bg.id}, 样品数: {bg.sample_count}")
            
            # 2. 创建质控样
            print(f"\n2. 为样品 {original_sample_id} 创建质控样...")
            sample_record_service = SampleRecordService(db)
            
            create_dto = QualityControlSampleCreateDTO(
                original_sample_id=original_sample_id,
                quality_control_types=["parallel_sample", "full_blank_sample"]
            )
            
            created_samples = await sample_record_service.create_quality_control_samples(
                create_dto, 
                create_by=1
            )
            
            print(f"   ✅ 成功创建 {len(created_samples)} 个质控样:")
            for sample in created_samples:
                print(f"      - 样品ID: {sample.id}, 样品编号: {sample.sample_number}, 类型: {sample.quality_control_type}")
            
            # 3. 检查质控样的瓶组关联
            print(f"\n3. 检查质控样的瓶组关联...")
            for sample in created_samples:
                qc_bottle_groups = await bottle_group_service.get_bottle_groups_by_sample(sample.id)
                print(f"   质控样 {sample.id} (样品编号: {sample.sample_number}) 关联了 {len(qc_bottle_groups)} 个瓶组:")
                
                if len(qc_bottle_groups) == len(original_bottle_groups):
                    print(f"      ✅ 瓶组数量匹配 ({len(qc_bottle_groups)} 个)")
                else:
                    print(f"      ❌ 瓶组数量不匹配! 原样品: {len(original_bottle_groups)}, 质控样: {len(qc_bottle_groups)}")
                
                for bg in qc_bottle_groups:
                    print(f"      - 瓶组编号: {bg.bottle_group_code}, 瓶组ID: {bg.id}, 样品数: {bg.sample_count}")
            
            # 4. 验证瓶组的样品数量是否更新
            print(f"\n4. 验证瓶组的样品数量是否更新...")
            for bg in original_bottle_groups:
                updated_bg = await bottle_group_service.get_bottle_group_detail(bg.id)
                if updated_bg:
                    expected_count = bg.sample_count + len(created_samples)
                    actual_count = updated_bg.sample_count
                    
                    if actual_count == expected_count:
                        print(f"   ✅ 瓶组 {bg.bottle_group_code} 样品数量已更新: {bg.sample_count} -> {actual_count}")
                    else:
                        print(f"   ❌ 瓶组 {bg.bottle_group_code} 样品数量不正确! 期望: {expected_count}, 实际: {actual_count}")
            
            print("\n" + "=" * 80)
            print("测试完成！")
            print("=" * 80)
            
        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()
            break


if __name__ == "__main__":
    asyncio.run(test_quality_control_bottle_groups())

