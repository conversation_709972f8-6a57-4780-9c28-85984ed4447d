-- 为样品记录表添加质控样相关字段
-- 创建时间：2025-01-26

-- 为 sample_record 表添加质控样相关字段
ALTER TABLE `sample_record` 
ADD COLUMN `is_quality_control` BOOLEAN DEFAULT FALSE COMMENT '是否为质控样：0-否，1-是',
ADD COLUMN `quality_control_type` VARCHAR(50) NULL COMMENT '质控样类型：parallel_sample-平行样，full_blank_sample-全程空白样，transport_blank_sample-运输空白样，equipment_blank_sample-设备清洗空白样，matrix_spike_sample-基体加标样，lab_parallel_sample-实验室平行样',
ADD COLUMN `related_sample_id` BIGINT NULL COMMENT '关联的原样品ID（质控样关联的原样品）';

-- 添加外键约束
ALTER TABLE `sample_record` 
ADD CONSTRAINT `fk_sample_record_related_sample` 
FOREIGN KEY (`related_sample_id`) REFERENCES `sample_record` (`id`) ON DELETE SET NULL;

-- 添加索引
CREATE INDEX `idx_sample_record_is_quality_control` ON `sample_record` (`is_quality_control`);
CREATE INDEX `idx_sample_record_related_sample_id` ON `sample_record` (`related_sample_id`);

-- 为PostgreSQL数据库的迁移脚本（如果使用PostgreSQL）
-- ALTER TABLE sample_record 
-- ADD COLUMN is_quality_control BOOLEAN DEFAULT FALSE,
-- ADD COLUMN quality_control_type VARCHAR(50),
-- ADD COLUMN related_sample_id BIGINT;

-- ALTER TABLE sample_record 
-- ADD CONSTRAINT fk_sample_record_related_sample 
-- FOREIGN KEY (related_sample_id) REFERENCES sample_record (id) ON DELETE SET NULL;

-- CREATE INDEX idx_sample_record_is_quality_control ON sample_record (is_quality_control);
-- CREATE INDEX idx_sample_record_related_sample_id ON sample_record (related_sample_id);

-- 添加字段注释（PostgreSQL）
-- COMMENT ON COLUMN sample_record.is_quality_control IS '是否为质控样：false-否，true-是';
-- COMMENT ON COLUMN sample_record.quality_control_type IS '质控样类型：parallel_sample-平行样，full_blank_sample-全程空白样，transport_blank_sample-运输空白样，equipment_blank_sample-设备清洗空白样，matrix_spike_sample-基体加标样，lab_parallel_sample-实验室平行样';
-- COMMENT ON COLUMN sample_record.related_sample_id IS '关联的原样品ID（质控样关联的原样品）';
