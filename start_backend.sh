#!/bin/bash

# LIMS后端启动脚本
echo "🚀 启动LIMS后端服务..."

# 进入后端目录
cd back

# 激活虚拟环境
echo "📦 激活Python虚拟环境..."
source venv/bin/activate

# 检查依赖
echo "🔍 检查Python依赖..."
pip list | grep fastapi > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ FastAPI未安装，正在安装依赖..."
    pip install -e .
fi

# 获取本机IP地址
LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}')
echo "🌐 本机IP地址: $LOCAL_IP"

# 启动服务
echo "🎯 启动后端服务 (端口: 9099)..."
echo "📍 局域网访问地址: http://$LOCAL_IP:9099/dev-api/docs"
echo "📍 本地访问地址: http://127.0.0.1:9099/dev-api/docs"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================="

python app.py
