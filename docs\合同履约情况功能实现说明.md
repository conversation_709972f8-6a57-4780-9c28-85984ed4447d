# 合同履约情况功能实现说明

## 功能概述

为合同详情页新增"履约情况"标签页，该功能主要用于查询合同关联的报价单所对应的所有任务单及其任务详情。

## 功能特点

### 1. 数据展示
- 自动获取合同关联的所有报价单
- 展示每个报价单对应的采样任务
- 显示任务名称、项目名称、报价单编号、客户名称等关键信息
- 实时显示任务状态和进度

### 2. 过滤功能
支持以下过滤条件：
- 项目编号
- 项目名称  
- 任务名称

### 3. 跳转功能
- **点击任务名称**：跳转到采样任务页面，查询条件的任务名称自动填充并进行查询
- **点击报价单编号**：跳转到项目报价页面，查询条件的报价单编号自动填充并进行查询
- **点击项目名称**：跳转到项目报价页面，查询条件的项目名称自动填充并进行查询

## 技术实现

### 1. 文件结构

```
front/src/views/contract/components/
├── ContractPerformance.vue          # 履约情况主组件（生产版本）
├── ContractPerformanceDemo.vue      # 履约情况演示组件（演示版本）
└── ContractDetail.vue               # 合同详情页（已添加履约情况标签页）

back/
├── test_contract_performance.py     # 后端接口测试脚本
└── docs/合同履约情况功能实现说明.md  # 本文档
```

### 2. 核心组件

#### ContractPerformance.vue
生产环境使用的主组件，包含完整的API调用逻辑：

**主要功能：**
- 获取合同关联报价单列表
- 根据报价单获取对应的采样任务
- 实现过滤和分页功能
- 提供跳转到其他页面的功能

**关键API调用：**
```javascript
// 获取合同关联报价单
import { listContractQuotationRelation } from '@/api/contract/contractQuotationRelation'

// 获取采样任务列表
import { pageSamplingTaskEnhanced } from '@/api/sampling/samplingTask'
```

#### ContractPerformanceDemo.vue
演示版本组件，使用模拟数据展示功能：

**特点：**
- 使用模拟数据，不依赖后端服务
- 完整展示UI界面和交互逻辑
- 包含功能说明和使用指南

### 3. 后端接口

该功能主要使用现有的后端接口，无需额外开发：

#### 合同关联报价单接口
```
GET /contract/quotation-relation/{contract_id}
```
获取指定合同关联的所有报价单列表。

#### 采样任务查询接口
```
GET /sampling/task/page-enhanced
```
增强版采样任务分页查询，支持按项目编号、项目名称、任务名称等条件过滤。

### 4. 数据流程

```mermaid
graph TD
    A[合同详情页] --> B[履约情况标签页]
    B --> C[获取合同关联报价单]
    C --> D[遍历每个报价单]
    D --> E[获取报价单对应的采样任务]
    E --> F[合并所有任务数据]
    F --> G[应用过滤条件]
    G --> H[展示任务列表]
    H --> I[点击跳转功能]
```

## 使用说明

### 1. 访问路径
1. 进入合同管理页面
2. 点击查看某个合同的详情
3. 在合同详情弹框中选择"履约情况"标签页

### 2. 功能操作
1. **查看任务列表**：页面自动加载合同关联的所有任务
2. **过滤任务**：使用顶部的过滤条件缩小查询范围
3. **跳转查看详情**：
   - 点击任务名称 → 跳转到采样任务页面
   - 点击项目名称 → 跳转到项目报价页面
   - 点击报价单编号 → 跳转到项目报价页面

### 3. 状态说明
任务状态显示：
- 🔵 待分配
- 🟡 已分配
- 🟢 执行中
- ✅ 已完成
- ❌ 已取消

## 测试验证

### 1. 后端接口测试
运行测试脚本验证后端接口：
```bash
cd back
python test_contract_performance.py
```

### 2. 前端功能测试
1. 启动前端服务
2. 访问合同详情页面
3. 切换到"履约情况"标签页
4. 验证数据展示和交互功能

## 部署说明

### 1. 生产环境部署
将 `ContractDetail.vue` 中的组件引用从 `ContractPerformanceDemo` 改回 `ContractPerformance`：

```vue
// 将演示组件
import ContractPerformanceDemo from './ContractPerformanceDemo.vue'

// 改为生产组件
import ContractPerformance from './ContractPerformance.vue'
```

### 2. 权限配置
确保用户具有以下权限：
- 合同查看权限
- 采样任务查看权限
- 项目报价查看权限

## 注意事项

1. **性能优化**：当合同关联的报价单较多时，建议实现分页加载
2. **错误处理**：已实现完善的错误提示和异常处理
3. **权限控制**：跳转功能会受到用户权限限制
4. **数据一致性**：任务状态实时更新，确保数据准确性

## 扩展功能

未来可考虑添加的功能：
1. 任务进度可视化图表
2. 履约率统计分析
3. 任务完成时间预测
4. 导出履约情况报告
5. 任务状态变更历史记录

## 技术支持

如有问题，请联系开发团队或查看相关技术文档。
