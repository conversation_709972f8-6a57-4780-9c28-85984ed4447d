-- 创建合同附件表
-- 执行时间：2024-11-01

CREATE TABLE `contract_attachment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `original_filename` varchar(255) NOT NULL COMMENT '原始文件名',
  `stored_filename` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) NOT NULL COMMENT '文件类型/MIME类型',
  `file_extension` varchar(20) NOT NULL COMMENT '文件扩展名',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_attachment_contract_id` (`contract_id`),
  KEY `idx_contract_attachment_create_time` (`create_time`),
  CONSTRAINT `fk_contract_attachment_contract_id` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同附件表';

-- 创建开票记录表
CREATE TABLE `contract_invoice` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `invoice_amount` decimal(15,2) NOT NULL COMMENT '开票金额',
  `invoice_date` date NOT NULL COMMENT '开票日期',
  `invoice_number` varchar(100) DEFAULT NULL COMMENT '发票号码',
  `invoice_type` varchar(50) DEFAULT NULL COMMENT '发票类型',
  `tax_rate` decimal(5,2) DEFAULT NULL COMMENT '税率(%)',
  `tax_amount` decimal(15,2) DEFAULT NULL COMMENT '税额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_invoice_contract_id` (`contract_id`),
  KEY `idx_contract_invoice_date` (`invoice_date`),
  CONSTRAINT `fk_contract_invoice_contract_id` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同开票记录表';

-- 创建回款记录表
CREATE TABLE `contract_payment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '回款金额',
  `payment_date` date NOT NULL COMMENT '回款日期',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '回款方式',
  `payment_account` varchar(100) DEFAULT NULL COMMENT '回款账户',
  `bank_info` varchar(200) DEFAULT NULL COMMENT '银行信息',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_payment_contract_id` (`contract_id`),
  KEY `idx_contract_payment_date` (`payment_date`),
  CONSTRAINT `fk_contract_payment_contract_id` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同回款记录表';

-- 创建收票记录表
CREATE TABLE `contract_receipt` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `receipt_amount` decimal(15,2) NOT NULL COMMENT '收票金额',
  `receipt_date` date NOT NULL COMMENT '收票日期',
  `receipt_number` varchar(100) DEFAULT NULL COMMENT '票据号码',
  `receipt_type` varchar(50) DEFAULT NULL COMMENT '票据类型',
  `supplier_name` varchar(200) DEFAULT NULL COMMENT '供应商名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_receipt_contract_id` (`contract_id`),
  KEY `idx_contract_receipt_date` (`receipt_date`),
  CONSTRAINT `fk_contract_receipt_contract_id` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同收票记录表';

-- 创建付款记录表
CREATE TABLE `contract_cost_payment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` int NOT NULL COMMENT '合同ID',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '付款金额',
  `payment_date` date NOT NULL COMMENT '付款日期',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式',
  `payee_name` varchar(200) DEFAULT NULL COMMENT '收款方名称',
  `payee_account` varchar(100) DEFAULT NULL COMMENT '收款账户',
  `bank_info` varchar(200) DEFAULT NULL COMMENT '银行信息',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_cost_payment_contract_id` (`contract_id`),
  KEY `idx_contract_cost_payment_date` (`payment_date`),
  CONSTRAINT `fk_contract_cost_payment_contract_id` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同付款记录表';

-- 为合同表添加合同成本字段
ALTER TABLE `contract` ADD COLUMN `contract_cost` decimal(15,2) DEFAULT NULL COMMENT '合同总成本' AFTER `contract_amount`;
