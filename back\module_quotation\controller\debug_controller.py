"""
调试控制器
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
import json

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from utils.response_util import ResponseUtil

router = APIRouter(
    prefix='/debug',
    tags=['调试']
)


@router.post('/echo', summary='回显请求数据')
async def echo_request(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    回显请求数据，用于调试

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 请求数据
    """
    # 获取请求体
    body = await request.json()
    
    # 获取请求头
    headers = dict(request.headers)
    
    # 获取查询参数
    query_params = dict(request.query_params)
    
    # 构造响应数据
    response_data = {
        'body': body,
        'headers': headers,
        'query_params': query_params
    }
    
    return ResponseUtil.success(data=response_data)
