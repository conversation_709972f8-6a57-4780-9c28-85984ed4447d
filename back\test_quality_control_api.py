"""
质控样API接口测试脚本
"""
import requests
import json


def test_quality_control_api():
    """测试质控样API接口"""
    base_url = "http://127.0.0.1:9099"
    headers = {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json"
    }
    
    print("开始测试质控样API接口...")

    # 0. 先检查是否有样品记录
    print("\n0. 检查样品记录")
    try:
        response = requests.get(
            f"{base_url}/sampling/sample-records/group/1",
            headers=headers
        )
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if response.status_code == 200 and result.get("code") == 200:
            samples = result.get("data", [])
            if samples:
                sample_id = samples[0].get("id")
                print(f"找到样品记录，使用样品ID: {sample_id}")
            else:
                print("没有找到样品记录，使用默认ID: 1")
                sample_id = 1
        else:
            print("获取样品记录失败，使用默认ID: 1")
            sample_id = 1
    except Exception as e:
        print(f"检查样品记录出现异常: {e}")
        sample_id = 1

    # 1. 测试创建质控样接口
    print("\n1. 测试创建质控样接口")
    create_data = {
        "originalSampleId": sample_id,
        "qualityControlTypes": ["parallel_sample", "full_blank_sample"],
        "pointName": "测试点位1"
    }

    try:
        response = requests.post(
            f"{base_url}/sampling/sample-records/quality-control/create",
            json=create_data,
            headers=headers
        )
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if response.status_code == 200 and result.get("code") == 200:
            print("✅ 创建质控样接口测试通过")
            created_samples = result.get("data", [])
            if created_samples:
                sample_id = created_samples[0].get("id")
                original_sample_id = created_samples[0].get("relatedSampleId")

                # 2. 测试获取质控样接口
                print(f"\n2. 测试获取质控样接口 (原样品ID: {original_sample_id})")
                get_response = requests.get(
                    f"{base_url}/sampling/sample-records/quality-control/original/{original_sample_id}",
                    headers=headers
                )
                get_result = get_response.json()
                print(f"状态码: {get_response.status_code}")
                print(f"响应: {json.dumps(get_result, ensure_ascii=False, indent=2)}")

                if get_response.status_code == 200 and get_result.get("code") == 200:
                    print("✅ 获取质控样接口测试通过")
                else:
                    print("❌ 获取质控样接口测试失败")

                # 3. 测试获取包含质控样的样品列表接口
                print(f"\n3. 测试获取包含质控样的样品列表接口")
                group_id = created_samples[0].get("groupId", 1)
                list_response = requests.get(
                    f"{base_url}/sampling/sample-records/group/{group_id}/with-quality-controls",
                    headers=headers
                )
                list_result = list_response.json()
                print(f"状态码: {list_response.status_code}")
                print(f"响应: {json.dumps(list_result, ensure_ascii=False, indent=2)}")

                if list_response.status_code == 200 and list_result.get("code") == 200:
                    print("✅ 获取包含质控样的样品列表接口测试通过")
                else:
                    print("❌ 获取包含质控样的样品列表接口测试失败")
        else:
            print("❌ 创建质控样接口测试失败")

    except Exception as e:
        print(f"❌ 接口测试出现异常: {e}")

    print("\n质控样API接口测试完成")


if __name__ == "__main__":
    test_quality_control_api()
