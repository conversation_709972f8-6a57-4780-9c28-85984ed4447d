{"name": "vfadmin", "version": "1.6.2", "description": "LIMS实验室管理系统", "author": "insistence", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/insistence2022/RuoYi-Vue3-FastAPI.git"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.31", "@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "ant-design-vue": "^4.1.1", "axios": "0.28.1", "clipboard": "2.0.11", "dayjs": "^1.11.13", "echarts": "5.5.1", "element-china-area-data": "^6.1.0", "element-plus": "^2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.15.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "qrcode": "^1.5.4", "splitpanes": "3.1.5", "text-encoding": "^0.7.0", "vue": "3.4.15", "vue-cropper": "1.1.1", "vue-qrcode": "^2.2.2", "vue-router": "4.4.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "@vue/compiler-sfc": "3.3.9", "less": "^4.2.0", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}