import request from '@/utils/request'

// 查询客户列表
export function listCustomer(query) {
  return request({
    url: '/customer/list',
    method: 'get',
    params: query
  })
}
  
// 查询客户详细
export function getCustomer(id) {
  return request({
    url: '/customer/' + id,
    method: 'get'
  })
}

// 新增客户
export function addCustomer(data) {
  return request({
    url: '/customer',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCustomer(data) {
  return request({
    url: '/customer',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCustomer(id) {
  return request({
    url: '/customer/' + id,
    method: 'delete'
  })
}

// 导出客户
export function exportCustomer(query) {
  return request({
    url: '/customer/export',
    method: 'get',
    params: query
  })
}

// 查询客户联系人列表
export function listCustomerContact(query) {
  return request({
    url: '/customer/contact/list',
    method: 'get',
    params: query
  })
}

// 查询客户联系人详细
export function getCustomerContact(id) {
  return request({
    url: '/customer/contact/' + id,
    method: 'get'
  })
}

// 新增客户联系人
export function addCustomerContact(data) {
  return request({
    url: '/customer/contact',
    method: 'post',
    data: data
  })
}

// 修改客户联系人
export function updateCustomerContact(data) {
  return request({
    url: '/customer/contact',
    method: 'put',
    data: data
  })
}

// 删除客户联系人
export function delCustomerContact(id) {
  return request({
    url: '/customer/contact/' + id,
    method: 'delete'
  })
}
