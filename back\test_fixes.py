#!/usr/bin/env python3
"""
测试合同模块修复的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_entity_imports():
    """测试实体类导入"""
    try:
        from module_contract.entity.do.contract_attachment_do import ContractAttachment
        from module_contract.entity.do.contract_financial_do import (
            ContractInvoice, ContractPayment, ContractReceipt, ContractCostPayment
        )
        print("✅ 实体类导入成功")
        
        # 检查是否移除了 del_flag 字段
        attachment = ContractAttachment()
        if hasattr(attachment, 'del_flag'):
            print("❌ ContractAttachment 仍然有 del_flag 字段")
            return False
        else:
            print("✅ ContractAttachment 已移除 del_flag 字段")
            
        invoice = ContractInvoice()
        if hasattr(invoice, 'del_flag'):
            print("❌ ContractInvoice 仍然有 del_flag 字段")
            return False
        else:
            print("✅ ContractInvoice 已移除 del_flag 字段")
            
        return True
        
    except ImportError as e:
        print(f"❌ 实体类导入失败: {e}")
        return False

def test_vo_imports():
    """测试VO模型导入"""
    try:
        from module_contract.entity.vo.contract_attachment_vo import (
            ContractAttachmentModel, ContractAttachmentUploadModel, 
            ContractAttachmentResponseModel, ContractAttachmentQueryModel
        )
        from module_contract.entity.vo.contract_financial_vo import (
            ContractInvoiceModel, ContractPaymentModel, 
            ContractReceiptModel, ContractCostPaymentModel
        )
        print("✅ VO模型导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ VO模型导入失败: {e}")
        return False

def test_service_imports():
    """测试服务类导入"""
    try:
        from module_contract.service.contract_attachment_service import ContractAttachmentService
        from module_contract.service.contract_financial_service import ContractFinancialService
        print("✅ 服务类导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 服务类导入失败: {e}")
        return False

def test_controller_imports():
    """测试控制器导入"""
    try:
        from module_contract.controller.contract_attachment_controller import contract_attachment as attachment_router
        from module_contract.controller.contract_financial_controller import contract_financial as financial_router
        print("✅ 控制器导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 控制器导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试合同模块修复...")
    print("=" * 50)
    
    tests = [
        test_entity_imports,
        test_vo_imports,
        test_service_imports,
        test_controller_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
