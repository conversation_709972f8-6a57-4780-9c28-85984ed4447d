"""
合同附件服务
"""

import os
import random
import shutil
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from fastapi import UploadFile, HTTPException
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_contract.entity.do.contract_attachment_do import ContractAttachment
from module_contract.entity.do.contract_do import Contract
from module_contract.entity.vo.contract_attachment_vo import (
    ContractAttachmentModel,
    ContractAttachmentResponseModel,
    ContractAttachmentListModel,
    ContractAttachmentQueryModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageUtil


class ContractAttachmentService:
    """合同附件服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.upload_dir = Path("attachedFiles/contract")
        # 确保上传目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)

    async def upload_attachments(
        self,
        contract_id: int,
        files: List[UploadFile],
        current_user: CurrentUserModel,
        remark: Optional[str] = None,
    ) -> CrudResponseModel:
        """
        上传合同附件

        :param contract_id: 合同ID
        :param files: 上传的文件列表
        :param current_user: 当前用户
        :param remark: 备注
        :return: 上传结果
        """
        try:
            # 验证合同是否存在
            contract = await self._get_contract_by_id(contract_id)
            if not contract:
                raise ServiceException(message=f"合同ID：{contract_id}不存在")

            # 验证文件
            if not files or len(files) == 0:
                raise ServiceException(message="请选择要上传的文件")

            uploaded_files = []
            
            for file in files:
                if not file.filename:
                    continue
                    
                # 验证文件大小（限制为50MB）
                if file.size and file.size > 50 * 1024 * 1024:
                    raise ServiceException(message=f"文件 {file.filename} 大小超过50MB限制")

                # 生成存储文件名：合同编号_随机4位数字
                stored_filename = self._generate_filename(contract.contract_number, file.filename)
                file_path = self.upload_dir / stored_filename

                # 保存文件
                await self._save_file(file, file_path)

                # 获取文件信息
                file_size = file_path.stat().st_size
                file_extension = Path(file.filename).suffix.lower()
                
                # 创建附件记录
                attachment = ContractAttachment(
                    contract_id=contract_id,
                    original_filename=file.filename,
                    stored_filename=stored_filename,
                    file_path=str(file_path),
                    file_size=file_size,
                    file_type=file.content_type or "application/octet-stream",
                    file_extension=file_extension,
                    upload_time=datetime.now(),
                    create_by=current_user.user.user_name if current_user and current_user.user else "",
                    create_time=datetime.now(),
                    update_by=current_user.user.user_name if current_user and current_user.user else "",
                    update_time=datetime.now(),
                    remark=remark,
                )

                self.db.add(attachment)
                await self.db.flush()
                
                uploaded_files.append({
                    "id": attachment.id,
                    "original_filename": file.filename,
                    "stored_filename": stored_filename,
                    "file_size": file_size,
                })

            await self.db.commit()

            return CrudResponseModel(
                is_success=True,
                message=f"成功上传{len(uploaded_files)}个文件",
                result={"uploaded_files": uploaded_files}
            )

        except Exception as e:
            await self.db.rollback()
            # 清理已上传的文件
            for file_info in uploaded_files:
                file_path = self.upload_dir / file_info["stored_filename"]
                if file_path.exists():
                    file_path.unlink()
            raise

    async def get_attachment_list(
        self, query_object: ContractAttachmentQueryModel
    ) -> ContractAttachmentListModel:
        """
        获取合同附件列表

        :param query_object: 查询参数
        :return: 附件列表
        """
        # 构建查询条件
        conditions = [
            ContractAttachment.contract_id == query_object.contract_id
        ]

        if query_object.original_filename:
            conditions.append(
                ContractAttachment.original_filename.like(f"%{query_object.original_filename}%")
            )
        if query_object.file_type:
            conditions.append(ContractAttachment.file_type.like(f"%{query_object.file_type}%"))

        # 执行查询
        stmt = (
            select(ContractAttachment)
            .where(and_(*conditions))
            .order_by(ContractAttachment.upload_time.desc())
        )

        if query_object.page_num and query_object.page_size:
            page_result = await PageUtil.paginate(
                self.db, stmt, query_object.page_num, query_object.page_size, True
            )
            attachments = page_result["records"]
            total = page_result["total"]
        else:
            result = await self.db.execute(stmt)
            attachments = result.scalars().all()
            total = len(attachments)

        # 计算总文件大小
        total_size = sum(attachment.file_size for attachment in attachments)

        # 转换为响应模型
        attachment_list = [
            ContractAttachmentResponseModel(**CamelCaseUtil.transform_result(attachment))
            for attachment in attachments
        ]

        return ContractAttachmentListModel(
            attachments=attachment_list,
            total=total,
            total_size=total_size
        )

    async def delete_attachment(self, attachment_id: int, current_user: CurrentUserModel) -> CrudResponseModel:
        """
        删除附件

        :param attachment_id: 附件ID
        :param current_user: 当前用户
        :return: 删除结果
        """
        try:
            # 获取附件信息
            stmt = select(ContractAttachment).where(ContractAttachment.id == attachment_id)
            result = await self.db.execute(stmt)
            attachment = result.scalars().first()

            if not attachment:
                raise ServiceException(message=f"附件ID：{attachment_id}不存在")

            # 删除物理文件
            file_path = Path(attachment.file_path)
            if file_path.exists():
                file_path.unlink()

            # 物理删除数据库记录
            await self.db.delete(attachment)
            await self.db.commit()

            return CrudResponseModel(
                is_success=True,
                message="删除成功",
                result={"id": attachment_id}
            )

        except Exception as e:
            await self.db.rollback()
            raise

    async def download_attachment(self, attachment_id: int) -> tuple[str, str, str]:
        """
        获取附件下载信息

        :param attachment_id: 附件ID
        :return: (文件路径, 原始文件名, 文件类型)
        """
        stmt = select(ContractAttachment).where(ContractAttachment.id == attachment_id)
        result = await self.db.execute(stmt)
        attachment = result.scalars().first()

        if not attachment:
            raise ServiceException(message=f"附件ID：{attachment_id}不存在")

        file_path = Path(attachment.file_path)
        if not file_path.exists():
            raise ServiceException(message="文件不存在")

        return str(file_path), attachment.original_filename, attachment.file_type

    def _generate_filename(self, contract_number: str, original_filename: str) -> str:
        """
        生成存储文件名：合同编号_随机4位数字.扩展名

        :param contract_number: 合同编号
        :param original_filename: 原始文件名
        :return: 存储文件名
        """
        # 获取文件扩展名
        file_extension = Path(original_filename).suffix
        
        # 生成随机4位数字
        random_digits = f"{random.randint(0, 9999):04d}"
        
        # 如果合同编号为空，使用默认前缀
        prefix = contract_number if contract_number else "CONTRACT"
        
        return f"{prefix}_{random_digits}{file_extension}"

    async def _save_file(self, file: UploadFile, file_path: Path) -> None:
        """
        保存上传的文件

        :param file: 上传的文件
        :param file_path: 保存路径
        """
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
        finally:
            file.file.close()

    async def _get_contract_by_id(self, contract_id: int) -> Optional[Contract]:
        """
        根据ID获取合同

        :param contract_id: 合同ID
        :return: 合同对象
        """
        stmt = select(Contract).where(Contract.id == contract_id)
        result = await self.db.execute(stmt)
        return result.scalars().first()
