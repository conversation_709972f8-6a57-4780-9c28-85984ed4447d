#!/bin/bash

# LIMS前端启动脚本
echo "🚀 启动LIMS前端服务..."

# 进入前端目录
cd front

# 检查Node.js和npm
echo "🔍 检查Node.js环境..."
node --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查依赖
echo "📦 检查npm依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 安装npm依赖..."
    npm install
fi

# 获取本机IP地址
LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}')
echo "🌐 本机IP地址: $LOCAL_IP"

# 启动开发服务器
echo "🎯 启动前端开发服务器 (端口: 4000)..."
echo "📍 局域网访问地址: http://$LOCAL_IP:4000"
echo "📍 本地访问地址: http://127.0.0.1:4000"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================="

npm run dev -- --host 0.0.0.0 --port 4000
