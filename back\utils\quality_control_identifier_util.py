"""
质控标识工具类
用于将质控样类型映射到质控标识
"""


class QualityControlIdentifierUtil:
    """质控标识工具类"""

    # 质控样类型到质控标识的映射
    QC_TYPE_IDENTIFIER_MAP = {
        "parallel_sample": "TP1",          # 现场平行样
        "lab_parallel_sample": "TP2",      # 实验室平行样
        "matrix_spike_sample": "JT",       # 基体加标样
        "full_blank_sample": "TK1",        # 全程空白样
        "transport_blank_sample": "TK2",   # 运输空白样
        "equipment_blank_sample": "TK3"    # 设备清洗空白样
    }
    
    @classmethod
    def get_identifier(cls, qc_type: str) -> str:
        """
        根据质控样类型获取质控标识
        
        Args:
            qc_type: 质控样类型
            
        Returns:
            质控标识，如果未找到则返回空字符串
        """
        if not qc_type:
            return ""
        
        # 去除首尾空格
        qc_type = qc_type.strip()
        
        # 从映射表中获取标识
        return cls.QC_TYPE_IDENTIFIER_MAP.get(qc_type, "")
    
    @classmethod
    def get_all_identifiers(cls) -> dict:
        """
        获取所有质控标识映射
        
        Returns:
            质控样类型到质控标识的映射字典
        """
        return cls.QC_TYPE_IDENTIFIER_MAP.copy()
    
    @classmethod
    def is_valid_qc_type(cls, qc_type: str) -> bool:
        """
        检查质控样类型是否有效
        
        Args:
            qc_type: 质控样类型
            
        Returns:
            是否为有效的质控样类型
        """
        if not qc_type:
            return False
        
        return qc_type.strip() in cls.QC_TYPE_IDENTIFIER_MAP

