# 频次数据获取逻辑说明

## 概述

本文档详细说明了LIMS系统中频次（frequency）数据的来源、存储、计算和展示逻辑。

## 数据来源

### 1. 原始数据表

频次数据存储在以下两个核心表中：

#### 1.1 `project_quotation_item` 表（项目报价明细表）

```sql
CREATE TABLE project_quotation_item (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_quotation_id INT NOT NULL COMMENT '项目报价ID',
    item_code VARCHAR(20) NOT NULL COMMENT '项目明细编号',
    
    -- 检测信息
    category VARCHAR(50) NOT NULL COMMENT '检测类别',
    parameter VARCHAR(100) NOT NULL COMMENT '检测参数',
    method VARCHAR(255) NOT NULL COMMENT '检测方法',
    
    -- 采样信息
    sample_source VARCHAR(50) COMMENT '样品来源',
    point_name VARCHAR(100) COMMENT '点位名称',
    point_count INT DEFAULT 1 COMMENT '点位数',
    cycle_type VARCHAR(10) COMMENT '检测周期类型',
    cycle_count INT DEFAULT 1 COMMENT '检测周期数',
    frequency INT DEFAULT 1 COMMENT '检测频次数',  -- 频次字段
    sample_count INT DEFAULT 1 COMMENT '样品数',
    
    -- 其他字段...
);
```

**说明：**
- `frequency` 字段存储检测频次数，默认值为1
- 这是频次数据的**原始来源**
- 在创建项目报价明细时由用户输入或系统默认设置

#### 1.2 `project_quotation_item_point_item` 表（项目报价明细点位项目表）

```sql
CREATE TABLE project_quotation_item_point_item (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_quotation_id INT NOT NULL COMMENT '项目报价ID',
    item_code VARCHAR(20) NOT NULL COMMENT '项目明细编号',
    
    -- 检测信息
    category VARCHAR(50) NOT NULL COMMENT '检测类别',
    parameter VARCHAR(100) NOT NULL COMMENT '检测参数',
    method VARCHAR(255) NOT NULL COMMENT '检测方法',
    
    -- 采样信息（拆分后的单个点位）
    sample_source VARCHAR(50) COMMENT '样品来源',
    point_name VARCHAR(100) COMMENT '拆分后的单个点位名称',
    point_count INT DEFAULT 1 COMMENT '点位数',
    cycle_type VARCHAR(10) COMMENT '检测周期类型',
    cycle_count INT DEFAULT 1 COMMENT '检测周期数',
    frequency INT DEFAULT 1 COMMENT '检测频次数',  -- 频次字段
    sample_count INT DEFAULT 1 COMMENT '样品数',
    
    -- 点位确认相关
    point_confirmed BOOLEAN DEFAULT FALSE COMMENT '点位是否已确认',
    confirmed_by VARCHAR(50) COMMENT '确认人',
    confirmed_time DATETIME COMMENT '确认时间',
    
    -- 其他字段...
);
```

**说明：**
- 这个表是从 `project_quotation_item` 表拆分点位后生成的
- 每个点位对应一条记录
- `frequency` 字段从原始明细表继承而来

## 数据流转

### 1. 项目报价阶段

```
用户创建项目报价
    ↓
填写检测明细（包括频次）
    ↓
保存到 project_quotation_item 表
    ↓
点位拆分（如果有多个点位）
    ↓
生成 project_quotation_item_point_item 记录
```

### 2. 采样任务阶段

```
创建采样任务
    ↓
选择项目报价和周期条目
    ↓
生成 detection_cycle_item 记录
    ↓
创建任务分组 sampling_task_group
    ↓
关联周期条目ID列表（cycle_item_ids）
```

### 3. 方案详情计算

```
获取任务分组详情
    ↓
解析 cycle_item_ids
    ↓
通过 detection_cycle_item 关联到 project_quotation_item_point_item
    ↓
查询并累加 frequency 字段
    ↓
返回 scheme_details.frequency
```

## 后端计算逻辑

### 1. 任务分组的方案详情计算

**文件：** `back/module_sampling/service/sampling_task_group_service.py`

**方法：** `_get_scheme_details_for_group(group: SamplingTaskGroup)`

```python
async def _get_scheme_details_for_group(self, group: SamplingTaskGroup):
    """
    获取分组的方案详情信息

    计算逻辑：
    1. 解析分组的 cycle_item_ids（周期条目ID列表）
    2. 通过 DetectionCycleItem 关联到 ProjectQuotationItemPointItem
    3. 查询每个点位明细的 frequency, sample_count, cycle_count, point_count
    4. 只取第一个周期条目的频次和样品数
    """

    # 解析周期条目ID列表
    cycle_item_ids = json.loads(group.cycle_item_ids)

    # 查询周期条目关联的点位明细信息
    stmt = select(
        ProjectQuotationItemPointItem.frequency,
        ProjectQuotationItemPointItem.sample_count,
        ProjectQuotationItemPointItem.cycle_count,
        ProjectQuotationItemPointItem.point_count
    ).select_from(
        DetectionCycleItem
    ).join(
        ProjectQuotationItemPointItem,
        DetectionCycleItem.project_quotation_item_point_item_id == ProjectQuotationItemPointItem.id
    ).where(DetectionCycleItem.id.in_(cycle_item_ids))

    result = await self.db.execute(stmt)
    rows = result.fetchall()

    # 只取第一个周期条目的频次和样品数
    if rows:
        first_row = rows[0]
        frequency = first_row[0] or 1  # 如果为空，默认为1
        sample_count = first_row[1] or 1
    else:
        frequency = 0
        sample_count = 0

    return {
        'scheme_details': {
            'frequency': frequency,
            'sample_count': sample_count,
            'cycle_count': 0,
            'point_count': 0
        }
    }
```

**关键点：**
- 频次是取第一个周期条目的 `frequency` 字段，不再累加
- 样品数也是取第一个周期条目的 `sample_count` 字段
- 如果第一个点位的频次为空，默认按1计算
- 周期数和点位数固定返回0（前端已不展示）

### 2. 项目报价的方案详情计算

**文件：** `back/module_quotation/service/project_quotation_service.py`

**方法：** `_get_scheme_details(quotation_id: int)`

```python
async def _get_scheme_details(self, quotation_id: int):
    """
    获取项目报价的方案详情统计信息
    
    计算逻辑：
    1. 查询项目报价的所有明细（project_quotation_item）
    2. 累加所有明细的 frequency, sample_count, cycle_count, point_count
    3. 返回总计数据
    """
    
    # 查询项目报价明细
    items_stmt = select(ProjectQuotationItem).where(
        ProjectQuotationItem.project_quotation_id == quotation_id
    )
    items_result = await self.db.execute(items_stmt)
    items = items_result.scalars().all()
    
    # 累加频次
    total_frequency = 0
    for item in items:
        frequency = item.frequency or 1  # 如果为空，默认为1
        total_frequency += frequency
    
    return {
        "scheme_details": {
            "total_frequency": total_frequency,
            "total_sample_count": total_sample_count,
            "total_cycle_count": total_cycle_count,
            "total_point_count": total_point_count,
            "total_items_count": len(items)
        }
    }
```

**关键点：**
- 这是在任务分配阶段使用的
- 统计整个项目报价的方案详情
- 字段名使用 `total_frequency` 前缀

## 前端展示

### 1. 采样执行页面

**文件：** `front/src/views/sampling/execution/index.vue`

**展示位置：**
- 表格列：方案详情列
- 详情弹窗：任务详情描述区域

**数据来源：**
```javascript
// 从分组详情API获取
const response = await getGroupDetail(groupId)
const schemeDetails = response.data.schemeDetails

// 展示
频次: {{ schemeDetails.frequency || 0 }}次
样品: {{ schemeDetails.sampleCount || 0 }}个
```

### 2. 任务分配页面

**文件：** `front/src/views/sampling/task-assignment/index.vue`

**展示位置：**
- 表格列：方案详情列
- 详情弹窗：方案详情统计区域

**数据来源：**
```javascript
// 从项目报价列表API获取
const response = await getApprovedQuotationsPage(queryParams)
const schemeDetails = row.schemeDetails

// 展示
频次: {{ schemeDetails.totalFrequency || 0 }}次
样品: {{ schemeDetails.totalSampleCount || 0 }}个
```

### 3. 小程序任务详情

**文件：** `miniprogram-native/pages/sampling/task-detail.wxml`

**数据来源：**
```javascript
// 从分组详情API获取
const response = await app.request({
  url: `/sampling/task-group/${groupId}`,
  method: 'GET'
})
const schemeDetails = response.data.schemeDetails

// 展示
频次：{{task.schemeDetails.frequency || 0}}
样品数：{{task.schemeDetails.sampleCount || 0}}
```

## API接口

### 1. 获取分组详情

**接口：** `GET /sampling/task-group/{group_id}`

**返回数据结构：**
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "taskName": "采样任务1",
    "groupCode": "25100003-1",
    "schemeDetails": {
      "frequency": 10,        // 频次总数
      "sampleCount": 20,      // 样品总数
      "cycleCount": 5,        // 周期总数（已不展示）
      "pointCount": 3         // 点位总数（已不展示）
    }
  }
}
```

### 2. 获取项目报价列表

**接口：** `GET /quotation/project-quotation/approved-page`

**返回数据结构：**
```json
{
  "code": 200,
  "data": {
    "rows": [
      {
        "id": 1,
        "projectName": "项目1",
        "schemeDetails": {
          "totalFrequency": 50,      // 总频次
          "totalSampleCount": 100,   // 总样品数
          "totalCycleCount": 10,     // 总周期数（已不展示）
          "totalPointCount": 5       // 总点位数（已不展示）
        }
      }
    ]
  }
}
```

## 数据关系图

```
project_quotation (项目报价)
    ↓
project_quotation_item (项目报价明细)
    ├─ frequency (频次) ← 原始数据
    ├─ sample_count (样品数)
    ├─ cycle_count (周期数)
    └─ point_count (点位数)
    ↓
project_quotation_item_point_item (点位明细)
    ├─ frequency (继承自明细)
    ├─ sample_count
    ├─ cycle_count
    └─ point_count
    ↓
detection_cycle_item (周期条目)
    └─ project_quotation_item_point_item_id (关联点位明细)
    ↓
sampling_task_group (任务分组)
    └─ cycle_item_ids (周期条目ID列表)
    ↓
scheme_details (方案详情 - 计算结果)
    ├─ frequency (累加所有点位明细的频次)
    └─ sample_count (累加所有点位明细的样品数)
```

## 总结

### 频次数据的完整流程

1. **数据录入**：用户在创建项目报价明细时输入频次
2. **数据存储**：存储在 `project_quotation_item.frequency` 字段
3. **点位拆分**：拆分点位时，频次数据复制到 `project_quotation_item_point_item.frequency`
4. **周期生成**：生成周期条目时，关联到点位明细
5. **任务分组**：创建任务分组时，记录周期条目ID列表
6. **方案计算**：查询分组详情时，通过周期条目关联到点位明细，**取第一个条目的频次和样品数**
7. **前端展示**：前端从API获取 `schemeDetails.frequency` 并展示

### 关键特点

- **取第一个条目**：频次和样品数是取第一个周期条目的数据，不再累加所有条目
- **默认值处理**：如果第一个条目的频次为空，默认按1计算；如果没有任何条目，返回0
- **数据继承**：从项目报价明细 → 点位明细 → 周期条目 → 任务分组
- **实时计算**：方案详情是在查询时实时计算的，不是预先存储的
- **周期数和点位数**：固定返回0，前端已不再展示这两个字段

