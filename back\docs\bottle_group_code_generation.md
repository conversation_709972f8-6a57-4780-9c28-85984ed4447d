# 瓶组编号生成逻辑

## 概述

瓶组编号是系统中用于唯一标识每个采样瓶组的编码。瓶组是根据样品的检测方法和瓶组配置自动生成的,一个样品可能对应多个瓶组(不同的检测方法需要不同的瓶子)。

## 编号格式

### 格式规则

**格式**: `{样品编号}B{序号}`

- **样品编号**: 完整的样品编号 (如: `2501001G1S001`)
- **B**: 固定字母,表示Bottle(瓶组)
- **序号**: 三位数字,表示该样品的第几个瓶组 (001~999)

### 示例

| 样品编号 | 瓶组序号 | 瓶组编号 | 说明 |
|---------|---------|----------|------|
| `2501001G1S001` | 第1个瓶组 | `2501001G1S001B001` | 样品1的第1个瓶组 |
| `2501001G1S001` | 第2个瓶组 | `2501001G1S001B002` | 样品1的第2个瓶组 |
| `2501001G1S001` | 第3个瓶组 | `2501001G1S001B003` | 样品1的第3个瓶组 |
| `2501001G1S002` | 第1个瓶组 | `2501001G1S002B001` | 样品2的第1个瓶组 |

## 生成逻辑

### 核心代码

位置: `back/module_sampling/service/sampling_bottle_group_service.py`

```python
# 用于记录每个样品的瓶组序号
sample_bottle_sequence = {}

for group_key, group_data in bottle_group_map.items():
    # 获取瓶组中第一个样品的编号（用于生成瓶组编号）
    first_sample = group_data['samples'][0]
    sample_number = first_sample['sample_number']
    sample_id = first_sample['sample_id']

    # 获取或初始化该样品的瓶组序号
    if sample_id not in sample_bottle_sequence:
        sample_bottle_sequence[sample_id] = 1
    else:
        sample_bottle_sequence[sample_id] += 1

    # 生成瓶组编号：样品编号 + B + 瓶组序号（3位）
    bottle_code = f"{sample_number}B{sample_bottle_sequence[sample_id]:03d}"

    # 创建瓶组记录
    bottle_group = SamplingBottleGroup(
        sampling_task_id=task_id,
        bottle_group_code=bottle_code,
        bottle_maintenance_id=group_data['bottle_maintenance_id'],
        detection_method=group_data['detection_method'],
        sample_count=len(group_data['samples']),
        create_by=create_by
    )
```

### 生成步骤

1. **获取样品信息**
   - 从瓶组数据中获取第一个样品的编号
   - 样品编号格式: `2501001G1S001`

2. **初始化或递增序号**
   - 检查该样品是否已有瓶组序号记录
   - 如果没有,初始化为1
   - 如果有,序号加1

3. **生成瓶组编号**
   - 拼接样品编号、字母B和序号
   - 序号格式化为3位数字 (不足3位前面补0)
   - 返回完整的瓶组编号

4. **创建瓶组记录**
   - 保存到 `sampling_bottle_group` 表
   - 关联样品记录

## 瓶组生成规则

### 1. 按检测方法分组

同一个样品,不同的检测方法需要不同的瓶子,因此会生成多个瓶组。

**示例**:
```
样品: 2501001G1S001 (水样)
检测方法1: pH值、溶解氧 → 瓶组1: 2501001G1S001B001
检测方法2: 重金属 → 瓶组2: 2501001G1S001B002
检测方法3: 有机物 → 瓶组3: 2501001G1S001B003
```

### 2. 瓶组配置匹配

系统会根据检测方法匹配瓶组配置(`bottle_maintenance`表):
- **匹配成功**: 使用配置的瓶子类型、容量、保存方式等
- **匹配失败**: 使用默认瓶组配置

### 3. 序号递增

每个样品的瓶组序号独立递增:
- 样品1: B001, B002, B003...
- 样品2: B001, B002, B003... (序号重新从001开始)

## 数据库表结构

### sampling_bottle_group 表

采样瓶组表,存储瓶组信息。

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 1 |
| sampling_task_id | BIGINT | 采样任务ID | 18 |
| bottle_group_code | VARCHAR(50) | 瓶组编号 | `2501001G1S001B001` |
| bottle_maintenance_id | INT | 瓶组配置ID | 5 (NULL表示默认瓶组) |
| detection_method | TEXT | 检测方法 | pH值、溶解氧 |
| sample_count | INT | 关联样品数量 | 1 |
| status | INT | 状态 | 0-采样,1-装箱,2-流转,3-完成 |
| create_by | BIGINT | 创建人 | 1 |
| create_time | DATETIME | 创建时间 | 2025-01-15 10:00:00 |

**约束**:
- `uk_bottle_group_code`: 瓶组编号唯一约束

### sampling_bottle_group_sample 表

瓶组与样品关联表,实现多对多关系。

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 1 |
| bottle_group_id | BIGINT | 瓶组ID | 1 |
| sample_record_id | BIGINT | 样品记录ID | 1 |
| create_time | DATETIME | 创建时间 | 2025-01-15 10:00:00 |

**约束**:
- `uk_bottle_sample`: (bottle_group_id, sample_record_id) 唯一约束

## 完整示例

### 场景: 一个水样品需要检测多个项目

**样品信息**:
- 样品编号: `2501001G1S001`
- 样品类型: 地表水
- 采样点位: 河流断面A

**检测项目**:
1. 常规指标: pH值、溶解氧、浊度
2. 重金属: 铅、镉、汞
3. 有机物: 苯、甲苯、二甲苯

**瓶组生成**:

| 瓶组编号 | 检测方法 | 瓶子类型 | 容量 | 保存方式 |
|---------|---------|---------|------|---------|
| `2501001G1S001B001` | pH值、溶解氧、浊度 | 塑料瓶 | 500ml | 常温 |
| `2501001G1S001B002` | 铅、镉、汞 | 玻璃瓶 | 1000ml | 冷藏 |
| `2501001G1S001B003` | 苯、甲苯、二甲苯 | 棕色玻璃瓶 | 500ml | 避光冷藏 |

### 场景: 多个样品的瓶组编号

**任务**: 2501001 (2025年1月第1个任务)
**分组**: 2501001G1 (第1个分组)

| 样品编号 | 瓶组序号 | 瓶组编号 | 检测方法 |
|---------|---------|----------|---------|
| `2501001G1S001` | 1 | `2501001G1S001B001` | pH值、溶解氧 |
| `2501001G1S001` | 2 | `2501001G1S001B002` | 重金属 |
| `2501001G1S002` | 1 | `2501001G1S002B001` | pH值、溶解氧 |
| `2501001G1S002` | 2 | `2501001G1S002B002` | 重金属 |
| `2501001G1S003` | 1 | `2501001G1S003B001` | pH值、溶解氧 |

## 编号层级关系

```
任务编号: 2501001
  ↓
分组编号: 2501001G1
  ↓
样品编号: 2501001G1S001
  ↓
瓶组编号: 2501001G1S001B001, 2501001G1S001B002, 2501001G1S001B003
```

## 特性

### 1. 基于样品编号

瓶组编号直接基于样品编号生成,便于追溯。

**优点**:
- 编号中包含完整的层级信息
- 可以直接从瓶组编号看出所属样品
- 便于样品和瓶组的关联管理

### 2. 序号独立递增

每个样品的瓶组序号独立计数。

**示例**:
```
样品1: B001, B002, B003
样品2: B001, B002 (序号重新从001开始)
样品3: B001
```

### 3. 唯一性保证

通过数据库唯一约束保证瓶组编号唯一。

**机制**:
- 数据库级别的唯一约束 (`uk_bottle_group_code`)
- 编号格式保证不会重复

### 4. 容量充足

每个样品最多支持999个瓶组。

**计算**:
- 序号范围: 001 ~ 999
- 每个样品最大瓶组数: 999个
- 实际使用: 通常每个样品3-10个瓶组

## 使用场景

### 1. 自动生成瓶组

在生成样品记录后,系统自动根据检测方法生成瓶组。

**流程**:
```
创建采样任务
  ↓
生成任务分组
  ↓
生成样品记录
  ↓
自动生成瓶组 (根据检测方法)
  ↓
打印瓶组标签
```

### 2. 瓶组标签打印

瓶组编号用于打印标签,贴在采样瓶上。

**标签内容**:
- 瓶组编号: `2501001G1S001B001`
- 样品类别: 地表水
- 采样点位: 河流断面A
- 检测项目: pH值、溶解氧
- 容器类型: 500ml塑料瓶
- 保存方式: 常温
- 二维码: 包含瓶组编号

### 3. 样品流转追踪

通过瓶组编号追踪样品的流转状态。

**状态**:
- 0: 采样 (已生成瓶组,待采样)
- 1: 装箱 (已采样,已装箱)
- 2: 流转 (运输中)
- 3: 完成 (已送达实验室)

## 相关编号

### 编号体系

```
任务编号 (Task Code)
  ↓
分组编号 (Group Code)
  ↓
样品编号 (Sample Number)
  ↓
质控样编号 (QC Sample Number)
  ↓
瓶组编号 (Bottle Group Code) ← 当前文档
```

### 编号对比

| 编号类型 | 格式 | 示例 | 长度 |
|---------|------|------|------|
| 任务编号 | `YYMMXXX` | `2501001` | 7位 |
| 分组编号 | `{任务编号}G{序号}` | `2501001G1` | 9位 |
| 样品编号 | `{分组编号}{类别}{序号}` | `2501001G1S001` | 14位 |
| 质控样编号 | `{样品编号}{质控标识}` | `2501001G1S001TP1` | 17位 |
| **瓶组编号** | **`{样品编号}B{序号}`** | **`2501001G1S001B001`** | **18位** |

## 注意事项

### 1. 编号长度

瓶组编号较长(18位),需要考虑:
- 标签打印空间
- 数据库字段长度
- 二维码容量

### 2. 序号管理

瓶组序号在内存中管理,需要注意:
- 并发生成时的序号冲突
- 序号的持久化(如果需要)

### 3. 瓶组配置

瓶组生成依赖瓶组配置:
- 确保瓶组配置完整
- 检测方法匹配规则清晰
- 默认瓶组配置可用

### 4. 样品关联

一个瓶组可能关联多个样品:
- 混合样品
- 平行样品
- 质控样品

## 常见问题

### Q1: 如何查询某个样品的所有瓶组?

```sql
SELECT bg.* 
FROM sampling_bottle_group bg
JOIN sampling_bottle_group_sample bgs ON bg.id = bgs.bottle_group_id
JOIN sample_record sr ON bgs.sample_record_id = sr.id
WHERE sr.sample_number = '2501001G1S001';
```

### Q2: 如何统计某个任务的瓶组数量?

```sql
SELECT COUNT(*) 
FROM sampling_bottle_group 
WHERE sampling_task_id = 18;
```

### Q3: 瓶组编号可以修改吗?

**不建议修改**,原因:
- 瓶组编号是唯一标识
- 标签已打印
- 修改会导致追溯困难

### Q4: 如何重新生成瓶组?

删除现有瓶组记录,重新调用生成接口:
```sql
-- 删除瓶组关联
DELETE FROM sampling_bottle_group_sample WHERE bottle_group_id IN (
    SELECT id FROM sampling_bottle_group WHERE sampling_task_id = 18
);

-- 删除瓶组
DELETE FROM sampling_bottle_group WHERE sampling_task_id = 18;
```

然后调用API重新生成。

## 相关文档

- [任务编号生成逻辑](./task_code_generation.md)
- [分组编号生成逻辑](../migrations/2025-10-27_update_group_code_format.md)
- [样品编号生成逻辑](./sample_number_generation.md)
- [质控样编号生成逻辑](./qc_sample_number_generation.md)
- [编号规则总览](./number_rules.md)

