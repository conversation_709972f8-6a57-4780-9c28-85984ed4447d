<template>
  <div class="contract-performance">
    <!-- 过滤条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 任务列表 -->
    <el-table
      v-loading="loading"
      :data="taskList"
      style="width: 100%"
      size="small"
      border
    >
      <el-table-column prop="taskName" label="任务名称" min-width="150">
        <template #default="scope">
          <el-link type="primary" @click="goToSamplingTask(scope.row.taskName)">
            {{ scope.row.taskName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="项目名称" min-width="200">
        <template #default="scope">
          <el-link type="primary" @click="goToProjectQuotation('projectName', scope.row.projectName)">
            {{ scope.row.projectName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="projectCode" label="报价单编号" min-width="150">
        <template #default="scope">
          <el-link type="primary" @click="goToProjectQuotation('projectCode', scope.row.projectCode)">
            {{ scope.row.projectCode }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户名称" min-width="200">
        <template #default="scope">
          {{ scope.row.customerName || scope.row.quotationCustomerName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="responsibleUserName" label="负责人" width="100" />
      <el-table-column prop="statusText" label="任务状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="plannedStartDate" label="计划开始日期" width="120" />
      <el-table-column prop="plannedEndDate" label="计划结束日期" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="150" />
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-show="total > 0"
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { listContractQuotationRelation } from '@/api/contract/contractQuotationRelation'
import { getTasksByProjectQuotationId, pageSamplingTaskEnhanced } from '@/api/sampling/samplingTask'

const router = useRouter()

const props = defineProps({
  contractId: {
    type: Number,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 数据
const loading = ref(false)
const taskList = ref([])
const total = ref(0)
const quotationList = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectCode: '',
  projectName: '',
  taskName: ''
})

// 获取合同关联的报价单列表
const getQuotationList = async () => {
  if (!props.contractId) return []
  
  try {
    const response = await listContractQuotationRelation(props.contractId)
    if (response.code === 200) {
      return response.data.rows || []
    }
  } catch (error) {
    console.error('获取合同关联报价单失败:', error)
  }
  return []
}

// 获取任务列表
const getList = async () => {
  loading.value = true
  try {
    // 先获取合同关联的报价单
    quotationList.value = await getQuotationList()

    if (quotationList.value.length === 0) {
      taskList.value = []
      total.value = 0
      return
    }

    // 获取所有关联项目的任务
    let allTasks = []

    // 遍历每个关联的报价单，获取对应的任务
    for (const quotation of quotationList.value) {
      try {
        // 根据项目报价ID获取任务列表（这里需要先通过项目编号找到项目报价ID）
        const taskResponse = await pageSamplingTaskEnhanced({
          page: 1,
          size: 1000, // 获取所有任务
          project_code: quotation.projectCode
        })

        if (taskResponse.code === 200 && taskResponse.data.records) {
          const tasks = taskResponse.data.records.map(task => ({
            ...task,
            statusText: getStatusText(task.status),
            // 补充报价单信息
            quotationProjectName: quotation.projectName,
            quotationCustomerName: quotation.customerName
          }))
          allTasks = allTasks.concat(tasks)
        }
      } catch (error) {
        console.error(`获取项目 ${quotation.projectCode} 的任务失败:`, error)
      }
    }

    // 应用过滤条件
    let filteredTasks = allTasks

    if (queryParams.value.taskName) {
      filteredTasks = filteredTasks.filter(task =>
        task.taskName && task.taskName.includes(queryParams.value.taskName)
      )
    }

    if (queryParams.value.projectName) {
      filteredTasks = filteredTasks.filter(task =>
        (task.projectName && task.projectName.includes(queryParams.value.projectName)) ||
        (task.quotationProjectName && task.quotationProjectName.includes(queryParams.value.projectName))
      )
    }

    if (queryParams.value.projectCode) {
      filteredTasks = filteredTasks.filter(task =>
        task.projectCode && task.projectCode.includes(queryParams.value.projectCode)
      )
    }

    // 分页处理
    const startIndex = (queryParams.value.pageNum - 1) * queryParams.value.pageSize
    const endIndex = startIndex + queryParams.value.pageSize

    taskList.value = filteredTasks.slice(startIndex, endIndex)
    total.value = filteredTasks.length

  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待分配',
    1: '已分配',
    2: '执行中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning', 
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return typeMap[status] || 'info'
}

// 跳转到采样任务页面
const goToSamplingTask = (taskName) => {
  // 跳转到采样任务页面，并自动填充任务名称进行查询
  router.push({
    path: '/sampling/task',
    query: {
      taskName: taskName
    }
  })
}

// 跳转到项目报价页面
const goToProjectQuotation = (type, value) => {
  const query = {}
  if (type === 'projectName') {
    query.projectName = value
  } else if (type === 'projectCode') {
    query.projectCode = value
  }
  
  // 跳转到项目报价页面，并自动填充查询条件
  router.push({
    path: '/quotation/project-quotation',
    query: query
  })
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    projectCode: '',
    projectName: '',
    taskName: ''
  }
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  queryParams.value.pageNum = 1
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  getList()
}

// 监听合同ID变化
watch(() => props.contractId, (newVal) => {
  if (newVal) {
    getList()
  }
}, { immediate: true })

onMounted(() => {
  if (props.contractId) {
    getList()
  }
})
</script>

<style scoped>
.contract-performance {
  padding: 20px;
}
</style>
