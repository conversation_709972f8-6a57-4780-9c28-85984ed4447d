# 样品编号生成问题修复说明

## 问题描述

**问题现象**：分组编号为 `25100005-5` 的任务，样品编号显示为 `1`，而不是预期的完整样品编号格式（如 `25100005S001`）。

**影响范围**：所有通过 `generate_sample_records_for_group()` 方法生成的样品记录。

## 问题原因

在 `back/module_sampling/service/sample_record_service.py` 文件的 `generate_sample_records_for_group()` 方法中（原第133行），样品编号的生成逻辑存在问题：

```python
# 问题代码（修复前）
for i in range(max_sample_count):
    sample_number = i + 1  # ❌ 只是简单的序号，不是完整的样品编号
    
    sample_record = SampleRecord(
        sampling_task_group_id=group_id,
        sample_number=sample_number,  # 这里存储的是 1, 2, 3...
        ...
    )
```

这导致样品编号只是简单的整数序号（1, 2, 3...），而不是按照设计规范的完整样品编号格式。

## 样品编号格式规范

### 格式定义

**样品编号格式**：`任务单号(8位) + 类别标识(1-2位) + 样品序号(3位)`

### 格式示例

```
25100005S001
│││││││││└─ 样品序号（3位，从001开始）
││││││││└── 类别标识（S = 水）
│└─────────── 任务单号（8位数字）
└────────────── 年月（2510 = 2025年10月）
```

### 类别标识映射

| 检测类别 | 类别标识 | 示例样品编号 |
|---------|---------|-------------|
| 水 | S | 25100005S001 |
| 气 | Q | 25100005Q001 |
| 土壤 | O | 25100005O001 |
| 固体废物 | O | 25100005O001 |
| 公共场所 | GG | 25100005GG001 |
| 辐射 | F | 25100005F001 |
| 其他 | O | 25100005O001 |

## 修复方案

### 1. 添加样品编号生成方法

在 `SampleRecordService` 类中添加 `_generate_sample_number()` 方法：

```python
def _generate_sample_number(self, task_code: str, detection_category: str, sequence: int) -> str:
    """
    生成样品编号
    格式：任务单号 + 类别标识 + 样品序号（3位）
    例如：2501001S001（任务2501001的第1个水样品）
    
    Args:
        task_code: 任务编号（8位数字，格式：YYMMXXXX）
        detection_category: 检测类别（如：水、气、土壤等）
        sequence: 样品序号（从1开始）
        
    Returns:
        完整的样品编号
    """
    from utils.category_identifier_util import CategoryIdentifierUtil
    
    # 获取类别标识
    category_identifier = CategoryIdentifierUtil.get_identifier(detection_category)
    
    # 生成样品编号：任务单号 + 类别标识 + 样品序号（3位）
    sample_number = f"{task_code}{category_identifier}{sequence:03d}"
    
    return sample_number
```

### 2. 修改样品记录生成逻辑

在 `generate_sample_records_for_group()` 方法中调用新方法：

```python
# 修复后的代码
# 生成样品记录
sample_records = []

# 获取任务编号和检测类别，用于生成样品编号
task_code = group.sampling_task.task_code
detection_category = representative_item.project_quotation_item_point_item.category

for i in range(max_sample_count):
    # 生成完整的样品编号：任务单号 + 类别标识 + 样品序号（3位）
    sample_number = self._generate_sample_number(task_code, detection_category, i + 1)
    
    # 生成一条样品记录
    sample_record = SampleRecord(
        sampling_task_group_id=group_id,
        sample_number=sample_number,  # ✅ 现在是完整的样品编号，如：25100005S001
        ...
    )
    sample_records.append(sample_record)
```

## 修复效果

### 修复前

```
分组编号：25100005-5
样品编号：1, 2, 3, 4, 5
```

### 修复后

```
分组编号：25100005-5
样品编号：25100005S001, 25100005S002, 25100005S003, 25100005S004, 25100005S005
```

## 测试验证

### 测试文件

- `back/tests/test_sample_number_generation_fix.py`

### 测试用例

```python
# 测试样品编号生成逻辑
test_cases = [
    {
        "task_code": "25100005",
        "detection_category": "水",
        "sequence": 1,
        "expected": "25100005S001"
    },
    {
        "task_code": "25100005",
        "detection_category": "水",
        "sequence": 5,
        "expected": "25100005S005"
    },
    {
        "task_code": "25100005",
        "detection_category": "气",
        "sequence": 1,
        "expected": "25100005Q001"
    },
    {
        "task_code": "25100005",
        "detection_category": "公共场所",
        "sequence": 10,
        "expected": "25100005GG010"
    }
]
```

### 运行测试

```bash
cd back
python -m pytest tests/test_sample_number_generation_fix.py -v -s
```

### 测试结果

```
✅ 任务25100005的第1个水样品
   任务编号: 25100005
   检测类别: 水
   类别标识: S
   样品序号: 1
   样品编号: 25100005S001

✅ 任务25100005的第5个水样品
   样品编号: 25100005S005

✅ 任务25100005的第1个气样品
   样品编号: 25100005Q001

✅ 任务25100005的第10个公共场所样品
   样品编号: 25100005GG010

✅ 所有测试用例通过！
```

## 相关文件

### 修改的文件

- `back/module_sampling/service/sample_record_service.py`
  - 添加 `_generate_sample_number()` 方法（第239-260行）
  - 修改 `generate_sample_records_for_group()` 方法（第130-156行）

### 依赖的工具类

- `back/utils/category_identifier_util.py` - 类别标识映射工具

### 测试文件

- `back/tests/test_sample_number_generation_fix.py` - 样品编号生成修复测试

## 注意事项

### 1. 数据库迁移

样品编号字段已从 `Integer` 类型改为 `String(100)` 类型。需要执行数据库迁移：

```sql
-- 文件位置: back/migrations/modify_sample_number_to_string.sql
ALTER TABLE sample_record 
MODIFY COLUMN sample_number VARCHAR(100) NOT NULL COMMENT '样品编号（格式：任务单号+类别标识+样品序号）';
```

### 2. 向后兼容

代码已做向后兼容处理，在 `_convert_to_dto()` 方法中会自动将整数类型的 `sample_number` 转换为字符串：

```python
sample_number=str(sample_record.sample_number) if sample_record.sample_number is not None else ''
```

### 3. 质控样编号

质控样编号格式：`原样品编号 + 质控标识`

示例：
- 原样品：`25100005S001`
- 平行样：`25100005S001TP`
- 全程空白样：`25100005S001TQ`

## 总结

此次修复解决了样品编号生成不符合规范的问题，确保所有样品都有完整的、符合格式要求的样品编号。修复后的样品编号格式统一、清晰，便于追溯和管理。

---

**修复日期**：2025-10-27  
**修复人员**：系统维护团队  
**测试状态**：✅ 已通过测试

