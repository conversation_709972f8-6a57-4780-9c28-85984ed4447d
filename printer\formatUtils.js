/**
 * 打印格式处理工具
 * 解决58mm纸张宽度适配、中英文混排、对齐等问题
 */

const ChineseTextEncoder = require('./textEncoder');

class PrintFormatUtils {
  constructor(options = {}) {
    // 58mm热敏纸配置
    this.config = {
      paperWidth: 58,           // 纸张宽度(mm)
      pageWidth: 384,           // 页面宽度(像素)
      maxCharsPerLine: 32,      // 每行最大字符数
      fontWidth: 1,             // 字体宽度倍数 (1或2)
      fontHeight: 1,            // 字体高度倍数 (1或2)
      ...options
    };
    
    this.textEncoder = new ChineseTextEncoder();
  }

  /**
   * 计算字符串显示宽度
   * @param {string} str - 字符串
   * @returns {number} 显示宽度
   */
  getStringWidth(str) {
    return this.textEncoder.getDisplayWidth(str);
  }

  /**
   * 获取当前字体设置下的每行字符数
   * @returns {number} 每行字符数
   */
  getCharsPerLine() {
    return Math.floor(this.config.maxCharsPerLine / this.config.fontWidth);
  }

  /**
   * 文本自动换行
   * @param {string} text - 原始文本
   * @param {number} maxWidth - 最大宽度（可选，默认使用当前字体设置）
   * @returns {Array<string>} 换行后的文本数组
   */
  wrapText(text, maxWidth = null) {
    if (!text) return [''];
    
    const lineWidth = maxWidth || this.getCharsPerLine();
    const lines = [];
    let currentLine = '';
    let currentWidth = 0;
    
    for (let i = 0; i < text.length; i++) {
      const char = text.charAt(i);
      const charWidth = /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
      
      // 检查是否需要换行
      if (currentWidth + charWidth > lineWidth) {
        lines.push(currentLine);
        currentLine = char;
        currentWidth = charWidth;
      } else {
        currentLine += char;
        currentWidth += charWidth;
      }
    }
    
    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine);
    }
    
    return lines.length > 0 ? lines : [''];
  }

  /**
   * 左右对齐（同一行显示两个内容）
   * @param {string} leftText - 左侧内容
   * @param {string} rightText - 右侧内容
   * @param {string} fillChar - 填充字符
   * @param {number} lineWidth - 行宽度（可选）
   * @returns {string} 格式化后的行
   */
  alignLeftRight(leftText, rightText, fillChar = ' ', lineWidth = null) {
    const maxWidth = lineWidth || this.getCharsPerLine();
    const leftWidth = this.getStringWidth(leftText);
    const rightWidth = this.getStringWidth(rightText);
    
    // 如果内容超出行宽，进行截取
    if (leftWidth + rightWidth > maxWidth) {
      const availableLeft = Math.floor((maxWidth - rightWidth) * 0.7);
      const availableRight = maxWidth - availableLeft;
      
      leftText = this.textEncoder.truncateByWidth(leftText, availableLeft);
      rightText = this.textEncoder.truncateByWidth(rightText, availableRight);
    }
    
    // 计算需要填充的字符数
    const usedWidth = this.getStringWidth(leftText) + this.getStringWidth(rightText);
    const fillCount = maxWidth - usedWidth;
    
    if (fillCount <= 0) {
      return leftText + rightText;
    }
    
    const fillStr = fillChar.repeat(fillCount);
    return leftText + fillStr + rightText;
  }

  /**
   * 居中对齐
   * @param {string} text - 要居中的文本
   * @param {string} fillChar - 填充字符
   * @param {number} lineWidth - 行宽度（可选）
   * @returns {string} 居中后的文本
   */
  alignCenter(text, fillChar = ' ', lineWidth = null) {
    const maxWidth = lineWidth || this.getCharsPerLine();
    const textWidth = this.getStringWidth(text);
    
    // 如果文本超出行宽，进行截取
    if (textWidth > maxWidth) {
      return this.textEncoder.truncateByWidth(text, maxWidth);
    }
    
    // 计算左右填充
    const totalFill = maxWidth - textWidth;
    const leftFill = Math.floor(totalFill / 2);
    const rightFill = totalFill - leftFill;
    
    return fillChar.repeat(leftFill) + text + fillChar.repeat(rightFill);
  }

  /**
   * 右对齐
   * @param {string} text - 要右对齐的文本
   * @param {string} fillChar - 填充字符
   * @param {number} lineWidth - 行宽度（可选）
   * @returns {string} 右对齐后的文本
   */
  alignRight(text, fillChar = ' ', lineWidth = null) {
    const maxWidth = lineWidth || this.getCharsPerLine();
    const textWidth = this.getStringWidth(text);
    
    // 如果文本超出行宽，进行截取
    if (textWidth > maxWidth) {
      return this.textEncoder.truncateByWidth(text, maxWidth);
    }
    
    const fillCount = maxWidth - textWidth;
    return fillChar.repeat(fillCount) + text;
  }

  /**
   * 生成分割线
   * @param {string} fillChar - 填充字符
   * @param {number} lineWidth - 行宽度（可选）
   * @returns {string} 分割线
   */
  createSeparatorLine(fillChar = '-', lineWidth = null) {
    const maxWidth = lineWidth || this.getCharsPerLine();
    return fillChar.repeat(maxWidth);
  }

  /**
   * 创建表格行
   * @param {Array<Object>} columns - 列配置 [{text: '', width: 10, align: 'left'}]
   * @param {string} separator - 列分隔符
   * @returns {string} 表格行
   */
  createTableRow(columns, separator = ' ') {
    const maxWidth = this.getCharsPerLine();
    let row = '';
    let usedWidth = 0;
    
    // 计算分隔符占用的宽度
    const separatorWidth = (columns.length - 1) * separator.length;
    const availableWidth = maxWidth - separatorWidth;
    
    // 处理每一列
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      let columnWidth = column.width || Math.floor(availableWidth / columns.length);
      let text = column.text || '';
      const align = column.align || 'left';
      
      // 确保不超出可用宽度
      if (usedWidth + columnWidth > availableWidth) {
        columnWidth = availableWidth - usedWidth;
      }
      
      // 截取文本
      text = this.textEncoder.truncateByWidth(text, columnWidth);
      
      // 对齐处理
      let formattedText = '';
      switch (align) {
        case 'center':
          formattedText = this.alignCenter(text, ' ', columnWidth);
          break;
        case 'right':
          formattedText = this.alignRight(text, ' ', columnWidth);
          break;
        default: // left
          const textWidth = this.getStringWidth(text);
          const padding = columnWidth - textWidth;
          formattedText = text + ' '.repeat(Math.max(0, padding));
      }
      
      row += formattedText;
      usedWidth += columnWidth;
      
      // 添加分隔符（除了最后一列）
      if (i < columns.length - 1) {
        row += separator;
      }
    }
    
    return row;
  }

  /**
   * 设置字体大小
   * @param {number} width - 字体宽度倍数 (1或2)
   * @param {number} height - 字体高度倍数 (1或2)
   */
  setFontSize(width, height) {
    this.config.fontWidth = Math.max(1, Math.min(2, width));
    this.config.fontHeight = Math.max(1, Math.min(2, height));
  }

  /**
   * 获取当前配置
   * @returns {Object} 当前配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

module.exports = PrintFormatUtils;
