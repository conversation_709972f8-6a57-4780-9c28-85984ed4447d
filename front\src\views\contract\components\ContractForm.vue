<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="基本信息" name="basic">
        <ContractBasic ref="contractBasicRef" v-model="form" />
      </el-tab-pane>
      <el-tab-pane label="关联报价单" name="quotation" v-if="form.id">
        <ContractQuotationRelation
          ref="contractQuotationRelationRef"
          :contract-id="form.id"
        />
      </el-tab-pane>
      <el-tab-pane label="商务信息" name="business" v-if="form.id">
        <ContractBusiness
          ref="contractBusinessRef"
          :contract-id="form.id"
        />
      </el-tab-pane>
      <el-tab-pane label="回款管理" name="payment" v-if="contractData.id">
          <ContractPaymentManagement
            :contract-id="contractData.id"
            :readonly="false"
          />
        </el-tab-pane>
        <el-tab-pane label="成本管理" name="cost" v-if="contractData.id">
          <ContractCostManagement
            :contract-id="contractData.id"
            :readonly="false"
          />
        </el-tab-pane>
      
    </el-tabs>

    <!-- 关闭按钮 -->
    <div style="text-align: center; margin-top: 20px;">
      <el-button @click="cancel">关 闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import ContractBasic from './ContractBasic.vue'
import ContractBusiness from './ContractBusiness.vue'
import ContractQuotationRelation from './ContractQuotationRelation.vue'
import ContractPaymentManagement from './ContractPaymentManagement.vue'
import ContractCostManagement from './ContractCostManagement.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  contractData: {
    type: Object,
    default: () => ({})
  },
})

const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 对话框显示状态
const open = ref(false)
const activeTab = ref('basic')

// 组件引用
const contractBasicRef = ref(null)
const contractBusinessRef = ref(null)
const contractQuotationRelationRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  contractName: '',
  contractNumber: '',
  externalContractNumber: '',
  businessType: '',
  regionProvince: '',
  regionCity: '',
  clientName: '',
  clientContact: '',
  acquisitionMethod: '',
  projectManagerIds: [],
  projectServiceId: null,
  deptId: null,
  contractSignDate: '',
  amountType: '',
  contractAmount: null,
  quotationTotalAmount: null,
  changedContractAmount: null,
  outsourcingAmount: null,
  outsourcingCompany: '',
  completionTime: '',
  contractStatus: '',
  remark: '',
  businessInfo: {
    departments: [],
    tasks: []
  }
})

// 初始化表单数据
const initFormData = () => {
  if (props.contractData && Object.keys(props.contractData).length > 0) {
    console.log("contractForm: 填充表单数据", props.contractData)
    Object.assign(form.value, props.contractData)
  } else {
    console.log("contractForm: 重置表单")
    resetForm()
  }
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  console.log("contractForm: visible", newVal)
  if (newVal) {
    // 弹框打开时初始化数据
    initFormData()
  }
}, { immediate: true })

// 监听contractData变化
watch(() => props.contractData, (newVal) => {
  console.log("contractForm: contractData changed", newVal)
  if (newVal && Object.keys(newVal).length > 0) {
    initFormData()
  }
}, { immediate: true, deep: true })



// 监听open变化
watch(open, (newVal) => {
  console.log("contractForm: open", open.value)
  emit('update:visible', newVal)
})



// 取消
const cancel = () => {
  open.value = false
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  Object.assign(form.value, {
    id: null,
    contractName: '',
    contractNumber: '',
    externalContractNumber: '',
    businessType: '',
    regionProvince: '',
    regionCity: '',
    clientName: '',
    clientContact: '',
    acquisitionMethod: '',
    projectManagerIds: [],
    projectServiceId: null,
    deptId: null,
    contractSignDate: '',
    amountType: '',
    contractAmount: null,
    quotationTotalAmount: null,
    changedContractAmount: null,
    outsourcingAmount: null,
    outsourcingCompany: '',
    completionTime: '',
    contractStatus: '',
    remark: '',
    businessInfo: {
      departments: [],
      tasks: []
    }
  })
  
  // 重置子组件表单
  if (contractBasicRef.value) {
    contractBasicRef.value.resetForm()
  }
}


</script>

<style scoped>
.app-container {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-tab-pane) {
  /* max-height: 600px; */
  overflow-y: auto;
}
</style>
