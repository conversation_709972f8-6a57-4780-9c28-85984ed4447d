import request from '@/utils/request'

// 查询技术手册类目列表
export function listTechnicalManualCategory(query) {
  return request({
    url: '/basedata/technical-manual-category/list',
    method: 'get',
    params: query
  })
}

// 查询技术手册类目分页列表
export function pageTechnicalManualCategory(query) {
  return request({
    url: '/basedata/technical-manual-category/page',
    method: 'get',
    params: query
  })
}

// 查询技术手册类目详细
export function getTechnicalManualCategory(id) {
  return request({
    url: '/basedata/technical-manual-category/' + id,
    method: 'get'
  })
}

// 新增技术手册类目
export function addTechnicalManualCategory(data) {
  return request({
    url: '/basedata/technical-manual-category',
    method: 'post',
    data: data
  })
}

// 修改技术手册类目
export function updateTechnicalManualCategory(data) {
  return request({
    url: '/basedata/technical-manual-category',
    method: 'put',
    data: data
  })
}

// 删除技术手册类目
export function delTechnicalManualCategory(id) {
  return request({
    url: '/basedata/technical-manual-category/' + id,
    method: 'delete'
  })
}

// 获取分类选项
export function getClassificationOptions() {
  return request({
      url: '/basedata/technical-manual-category/options/classifications',
    method: 'get'
  })
}

// 根据分类获取检测类别选项
export function getCategoryOptionsByClassification(classification) {
  return request({
    url: '/basedata/technical-manual-category/options/categories',
    method: 'get',
    params: { classification }
  })
}

// 获取类目树形数据
export function getCategoryTree() {
  return request({
    url: '/basedata/technical-manual-category/tree',
    method: 'get'
  })
}

// 导出技术手册类目
export function exportTechnicalManualCategory(query) {
  return request({
    url: '/basedata/technical-manual-category/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
