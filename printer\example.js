/**
 * 小程序蓝牙打印使用示例
 * 展示如何解决中文乱码、数据分包、格式处理等问题
 */

const PrinterManager = require('./printerManager');

// 在页面中使用示例
Page({
  data: {
    connected: false,
    deviceInfo: null,
    printerManager: null
  },

  onLoad() {
    // 初始化打印管理器
    this.data.printerManager = new PrinterManager({
      // 蓝牙传输配置
      bluetooth: {
        maxChunkSize: 20,    // 最大分包大小
        delay: 20,           // 分包延时
        retryCount: 3        // 重试次数
      },
      // 格式配置
      format: {
        maxCharsPerLine: 32, // 每行最大字符数
        fontWidth: 1,        // 字体宽度
        fontHeight: 1        // 字体高度
      },
      // 其他配置
      autoWrap: true,        // 自动换行
      debug: true            // 调试模式
    });
  },

  /**
   * 连接蓝牙设备后调用
   */
  onDeviceConnected(deviceInfo) {
    this.setData({
      connected: true,
      deviceInfo: deviceInfo
    });
    
    // 设置设备信息到打印管理器
    this.data.printerManager.setDeviceInfo(deviceInfo);
    
    wx.showToast({
      title: '设备连接成功',
      icon: 'success'
    });
  },

  /**
   * 打印测试文本
   */
  async printTestText() {
    try {
      wx.showLoading({ title: '打印中...' });
      
      const manager = this.data.printerManager;
      
      // 1. 打印标题
      await manager.printCenter('测试打印', {
        fontSize: { width: 2, height: 2 },
        bold: true
      });
      
      // 2. 打印分割线
      await manager.printSeparator('=');
      
      // 3. 打印中文内容
      await manager.printText('这是中文测试内容，包含各种字符：！@#￥%……&*（）');
      
      // 4. 打印左右对齐
      await manager.printLeftRight('商品名称', '价格');
      await manager.printLeftRight('宫保鸡丁', '¥28.00');
      await manager.printLeftRight('麻婆豆腐', '¥18.00');
      
      // 5. 打印分割线
      await manager.printSeparator('-');
      
      // 6. 打印总计
      await manager.printLeftRight('总计', '¥46.00');
      
      wx.hideLoading();
      wx.showToast({
        title: '打印完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '打印失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 打印表格示例
   */
  async printTable() {
    try {
      wx.showLoading({ title: '打印中...' });
      
      const manager = this.data.printerManager;
      
      // 表格数据
      const tableData = [
        ['商品1', '2', '20.00'],
        ['商品2', '1', '15.00'],
        ['商品3', '3', '30.00']
      ];
      
      // 列配置
      const columns = [
        { title: '商品', width: 16, align: 'left' },
        { title: '数量', width: 6, align: 'center' },
        { title: '金额', width: 10, align: 'right' }
      ];
      
      await manager.printTable(tableData, columns, {
        showHeader: true,
        separator: ' ',
        headerSeparator: true
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '表格打印完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '打印失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 打印完整小票
   */
  async printFullReceipt() {
    try {
      wx.showLoading({ title: '打印中...' });
      
      await this.data.printerManager.printReceipt();
      
      wx.hideLoading();
      wx.showToast({
        title: '小票打印完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '打印失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 自定义打印任务
   */
  async printCustomJob() {
    try {
      wx.showLoading({ title: '打印中...' });
      
      const manager = this.data.printerManager;
      const job = manager.createPrintJob();
      
      // 自定义打印内容
      job.setAlign('ct').setSize(2, 1);
      job.println('自定义打印');
      
      job.setAlign('lt').setSize(1, 1);
      job.println('支持中文：你好世界！');
      job.println('支持英文：Hello World!');
      job.println('支持数字：1234567890');
      job.println('支持符号：!@#$%^&*()');
      
      // 发送打印任务
      await manager.sendPrintJob(job, {
        onProgress: (progress) => {
          console.log(`打印进度: ${progress.percentage}%`);
        }
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '自定义打印完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '打印失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 测试长文本打印（自动换行）
   */
  async printLongText() {
    try {
      wx.showLoading({ title: '打印中...' });
      
      const longText = '这是一段很长的中文文本，用来测试自动换行功能。当文本超过一行的最大字符数时，应该自动换行显示。这样可以确保所有内容都能正确打印出来，不会因为超出纸张宽度而丢失。';
      
      await this.data.printerManager.printText(longText, {
        fontSize: { width: 1, height: 1 }
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '长文本打印完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '打印失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 获取打印状态
   */
  getPrintStatus() {
    const status = this.data.printerManager.getConnectionStatus();
    console.log('打印状态:', status);
    
    wx.showModal({
      title: '打印状态',
      content: JSON.stringify(status, null, 2),
      showCancel: false
    });
  }
});

// 导出供其他页面使用
module.exports = {
  PrinterManager,
  
  // 快速创建打印管理器的工厂函数
  createPrinterManager: (options = {}) => {
    return new PrinterManager({
      bluetooth: {
        maxChunkSize: 20,
        delay: 20,
        retryCount: 3,
        ...options.bluetooth
      },
      format: {
        maxCharsPerLine: 32,
        fontWidth: 1,
        fontHeight: 1,
        ...options.format
      },
      autoWrap: true,
      debug: false,
      ...options
    });
  }
};
