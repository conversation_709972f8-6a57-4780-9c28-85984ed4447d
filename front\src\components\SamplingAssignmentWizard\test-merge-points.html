<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>点位合并测试</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    .point-tag {
      display: inline-block;
      padding: 5px 12px;
      margin: 4px;
      border-radius: 4px;
      background-color: #f0f0f0;
      border: 1px solid #ddd;
    }
    .point-tag.warning {
      background-color: #fdf6ec;
      border-color: #e6a23c;
      color: #e6a23c;
    }
    .point-count {
      margin-left: 4px;
      font-weight: bold;
      color: #e6a23c;
    }
    h2 {
      color: #333;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }
    h3 {
      color: #666;
    }
    .original-list, .merged-list {
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div id="app">
    <h1>点位合并展示测试</h1>
    
    <div class="test-section">
      <h2>测试场景1：多个相同点位</h2>
      <h3>原始点位列表（{{ testData1.length }} 个）：</h3>
      <div class="original-list">
        <span v-for="(point, index) in testData1" :key="index" class="point-tag">
          {{ point.pointName }}
        </span>
      </div>
      
      <h3>合并后点位列表（{{ getMergedPoints(testData1).length }} 个不同点位）：</h3>
      <div class="merged-list">
        <span 
          v-for="pointInfo in getMergedPoints(testData1)" 
          :key="pointInfo.name" 
          class="point-tag"
          :class="{ 'warning': pointInfo.count > 1 }"
        >
          {{ pointInfo.name }}
          <span v-if="pointInfo.count > 1" class="point-count">×{{ pointInfo.count }}</span>
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>测试场景2：所有点位都不同</h2>
      <h3>原始点位列表（{{ testData2.length }} 个）：</h3>
      <div class="original-list">
        <span v-for="(point, index) in testData2" :key="index" class="point-tag">
          {{ point.pointName }}
        </span>
      </div>
      
      <h3>合并后点位列表（{{ getMergedPoints(testData2).length }} 个不同点位）：</h3>
      <div class="merged-list">
        <span 
          v-for="pointInfo in getMergedPoints(testData2)" 
          :key="pointInfo.name" 
          class="point-tag"
          :class="{ 'warning': pointInfo.count > 1 }"
        >
          {{ pointInfo.name }}
          <span v-if="pointInfo.count > 1" class="point-count">×{{ pointInfo.count }}</span>
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>测试场景3：混合场景</h2>
      <h3>原始点位列表（{{ testData3.length }} 个）：</h3>
      <div class="original-list">
        <span v-for="(point, index) in testData3" :key="index" class="point-tag">
          {{ point.pointName }}
        </span>
      </div>
      
      <h3>合并后点位列表（{{ getMergedPoints(testData3).length }} 个不同点位）：</h3>
      <div class="merged-list">
        <span 
          v-for="pointInfo in getMergedPoints(testData3)" 
          :key="pointInfo.name" 
          class="point-tag"
          :class="{ 'warning': pointInfo.count > 1 }"
        >
          {{ pointInfo.name }}
          <span v-if="pointInfo.count > 1" class="point-count">×{{ pointInfo.count }}</span>
        </span>
      </div>
    </div>
  </div>

  <script>
    const { createApp } = Vue

    createApp({
      data() {
        return {
          // 测试场景1：多个相同点位
          testData1: [
            { id: 1, pointName: '车间A' },
            { id: 2, pointName: '车间A' },
            { id: 3, pointName: '车间A' },
            { id: 4, pointName: '车间B' },
            { id: 5, pointName: '车间B' },
            { id: 6, pointName: '办公室' }
          ],
          
          // 测试场景2：所有点位都不同
          testData2: [
            { id: 1, pointName: '车间A' },
            { id: 2, pointName: '车间B' },
            { id: 3, pointName: '车间C' },
            { id: 4, pointName: '办公室' },
            { id: 5, pointName: '仓库' }
          ],
          
          // 测试场景3：混合场景
          testData3: [
            { id: 1, pointName: '1#排放口' },
            { id: 2, pointName: '1#排放口' },
            { id: 3, pointName: '1#排放口' },
            { id: 4, pointName: '1#排放口' },
            { id: 5, pointName: '2#排放口' },
            { id: 6, pointName: '2#排放口' },
            { id: 7, pointName: '3#排放口' },
            { id: 8, pointName: '厂界东' },
            { id: 9, pointName: '厂界西' },
            { id: 10, pointName: '厂界南' },
            { id: 11, pointName: '厂界北' }
          ]
        }
      },
      methods: {
        getMergedPoints(selectedPointItems) {
          const pointNameMap = new Map()
          
          // 统计每个点位名称出现的次数
          selectedPointItems.forEach(point => {
            const name = point.pointName
            if (pointNameMap.has(name)) {
              pointNameMap.set(name, pointNameMap.get(name) + 1)
            } else {
              pointNameMap.set(name, 1)
            }
          })
          
          // 转换为数组格式，按名称排序
          return Array.from(pointNameMap.entries())
            .map(([name, count]) => ({ name, count }))
            .sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
        }
      }
    }).mount('#app')
  </script>
</body>
</html>

