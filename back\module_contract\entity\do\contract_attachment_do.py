"""
合同附件数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class ContractAttachment(Base):
    """
    合同附件表
    """

    __tablename__ = "contract_attachment"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    original_filename = Column(String(255), nullable=False, comment="原始文件名")
    stored_filename = Column(String(255), nullable=False, comment="存储文件名")
    file_path = Column(String(500), nullable=False, comment="文件存储路径")
    file_size = Column(BigInteger, nullable=False, comment="文件大小(字节)")
    file_type = Column(String(100), nullable=False, comment="文件类型/MIME类型")
    file_extension = Column(String(20), nullable=False, comment="文件扩展名")
    upload_time = Column(DateTime, nullable=False, comment="上传时间")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 关联关系
    contract = relationship("Contract", back_populates="attachments")
