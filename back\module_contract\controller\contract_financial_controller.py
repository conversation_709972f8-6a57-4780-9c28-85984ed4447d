"""
合同财务管理控制器
"""

from fastapi import APIRouter, Depends, Request, Path as PathParam
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_contract.entity.vo.contract_financial_vo import (
    ContractFinancialSummaryModel,
    ContractPaymentSummaryModel,
    ContractCostSummaryModel,
    ContractInvoiceModel,
    ContractPaymentModel,
    ContractReceiptModel,
    ContractCostPaymentModel,
)
from module_contract.service.contract_financial_service import ContractFinancialService
from utils.response_util import ResponseUtil

# 创建路由
contract_financial = APIRouter(prefix="/contract/financial", tags=["合同财务管理"])


@contract_financial.get("/summary/{contract_id}", response_model=ContractFinancialSummaryModel, summary="获取合同财务汇总")
async def get_contract_financial_summary(
    request: Request,
    contract_id: int = PathParam(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取合同财务汇总信息

    :param request: 请求对象
    :param contract_id: 合同ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 财务汇总信息
    """
    service = ContractFinancialService(db)
    summary = await service.get_financial_summary(contract_id)
    return ResponseUtil.success(data=summary)


@contract_financial.get("/payment/summary/{contract_id}", response_model=ContractPaymentSummaryModel, summary="获取合同回款汇总")
async def get_contract_payment_summary(
    request: Request,
    contract_id: int = PathParam(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取合同回款汇总信息

    :param request: 请求对象
    :param contract_id: 合同ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 回款汇总信息
    """
    service = ContractFinancialService(db)
    summary = await service.get_payment_summary(contract_id)
    return ResponseUtil.success(data=summary)


@contract_financial.get("/cost/summary/{contract_id}", response_model=ContractCostSummaryModel, summary="获取合同成本汇总")
async def get_contract_cost_summary(
    request: Request,
    contract_id: int = PathParam(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取合同成本汇总信息

    :param request: 请求对象
    :param contract_id: 合同ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 成本汇总信息
    """
    service = ContractFinancialService(db)
    summary = await service.get_cost_summary(contract_id)
    return ResponseUtil.success(data=summary)


@contract_financial.post("/invoice", response_model=CrudResponseModel, summary="新增开票记录")
async def add_contract_invoice(
    request: Request,
    invoice: ContractInvoiceModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增开票记录

    :param request: 请求对象
    :param invoice: 开票记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = ContractFinancialService(db)
    result = await service.add_invoice(invoice, current_user)
    return ResponseUtil.success(data=result)


@contract_financial.post("/payment", response_model=CrudResponseModel, summary="新增回款记录")
async def add_contract_payment(
    request: Request,
    payment: ContractPaymentModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增回款记录

    :param request: 请求对象
    :param payment: 回款记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = ContractFinancialService(db)
    result = await service.add_payment(payment, current_user)
    return ResponseUtil.success(data=result)


@contract_financial.post("/receipt", response_model=CrudResponseModel, summary="新增收票记录")
async def add_contract_receipt(
    request: Request,
    receipt: ContractReceiptModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增收票记录

    :param request: 请求对象
    :param receipt: 收票记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = ContractFinancialService(db)
    result = await service.add_receipt(receipt, current_user)
    return ResponseUtil.success(data=result)


@contract_financial.post("/cost-payment", response_model=CrudResponseModel, summary="新增付款记录")
async def add_contract_cost_payment(
    request: Request,
    cost_payment: ContractCostPaymentModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增付款记录

    :param request: 请求对象
    :param cost_payment: 付款记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = ContractFinancialService(db)
    result = await service.add_cost_payment(cost_payment, current_user)
    return ResponseUtil.success(data=result)
