"""
测试质控样创建功能（不需要点位名称）
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dto.sample_record_dto import QualityControlSampleCreateDTO


async def test_create_quality_control_samples():
    """测试创建质控样（不需要点位名称）"""
    print("=" * 80)
    print("测试质控样创建功能（不需要点位名称）")
    print("=" * 80)
    
    # 获取数据库会话
    async for db in get_db():
        try:
            # 创建服务实例
            service = SampleRecordService(db)
            
            # 1. 检查原样品是否存在
            print("\n1. 检查原样品记录...")
            original_sample = await service.sample_record_dao.get_sample_record_by_id(1)
            if not original_sample:
                print("❌ 原样品记录不存在")
                return
            
            print(f"✅ 找到原样品记录:")
            print(f"   ID: {original_sample.id}")
            print(f"   样品编号: {original_sample.sample_number}")
            print(f"   点位名称: {original_sample.point_name}")
            
            # 2. 创建质控样DTO（不包含点位名称）
            print("\n2. 创建质控样（不需要输入点位名称）...")
            create_dto = QualityControlSampleCreateDTO(
                original_sample_id=1,
                quality_control_types=["parallel_sample", "full_blank_sample"]
            )
            
            print(f"   原样品ID: {create_dto.original_sample_id}")
            print(f"   质控样类型: {create_dto.quality_control_types}")
            print(f"   点位名称: 从原样品继承")
            
            # 3. 调用服务创建质控样
            print("\n3. 调用服务创建质控样...")
            results = await service.create_quality_control_samples(create_dto, create_by=1)
            
            print(f"✅ 质控样创建成功，共创建 {len(results)} 个质控样:")
            for i, result in enumerate(results, 1):
                print(f"\n   质控样 {i}:")
                print(f"   - ID: {result.id}")
                print(f"   - 样品编号: {result.sample_number}")
                print(f"   - 点位名称: {result.point_name}")
                print(f"   - 质控样类型: {result.quality_control_type}")
                print(f"   - 关联原样品ID: {result.related_sample_id}")
            
            # 4. 验证点位名称是否从原样品继承
            print("\n4. 验证点位名称是否从原样品继承...")
            all_inherited = all(r.point_name == original_sample.point_name for r in results)
            if all_inherited:
                print(f"✅ 所有质控样的点位名称都正确继承自原样品: {original_sample.point_name}")
            else:
                print("❌ 质控样的点位名称未正确继承")
            
            print("\n" + "=" * 80)
            print("测试完成！")
            print("=" * 80)
            
        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            break


if __name__ == "__main__":
    asyncio.run(test_create_quality_control_samples())

