from sqlalchemy import Column, BigInteger, Integer, String, Text, DateTime, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingTaskGroup(Base):
    """采样任务分组表"""
    __tablename__ = 'sampling_task_group'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')

    # 分组编号字段
    group_code = Column(String(100), nullable=False, unique=True, comment='分组编号，格式：采样任务编号G分组序号')

    # 分组字段
    cycle_number = Column(Integer, nullable=False, comment='周期序号')
    cycle_type = Column(String(50), comment='周期类型')
    detection_category = Column(String(100), comment='检测类别')
    point_name = Column(String(200), comment='点位名称')
    
    # 关联的周期条目ID列表
    cycle_item_ids = Column(Text, comment='关联的周期条目ID列表（JSON格式）')
    
    # 执行人指派信息
    assigned_user_ids = Column(Text, comment='分配的执行人ID列表（JSON格式）')

    # 分组状态字段
    status = Column(Integer, default=0, comment='分组状态：0-待执行，1-执行中，2-已完成')

    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    sampling_task = relationship("SamplingTask", back_populates="task_groups")

    # 创建人和更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引和约束
    __table_args__ = (
        Index('idx_sampling_task_group_task_id', 'sampling_task_id'),
        Index('idx_sampling_task_group_cycle', 'cycle_number', 'cycle_type', 'detection_category', 'point_name'),
        Index('idx_sampling_task_group_status', 'status'),
        Index('idx_sampling_task_group_code', 'group_code'),
        UniqueConstraint('sampling_task_id', 'cycle_number', 'cycle_type', 'detection_category', 'point_name',
                        name='uk_task_group'),
        UniqueConstraint('group_code', name='uk_group_code'),
        {'comment': '采样任务分组表'}
    )
    
    def __repr__(self):
        return f"<SamplingTaskGroup(id={self.id}, sampling_task_id={self.sampling_task_id}, cycle_number={self.cycle_number}, detection_category='{self.detection_category}', point_name='{self.point_name}', status={self.status})>"

    @property
    def status_label(self):
        """状态标签"""
        status_map = {
            0: '待执行',
            1: '执行中',
            2: '已完成'
        }
        return status_map.get(self.status, '未知状态')
    
    def to_dict(self):
        """转换为字典"""
        import json
        
        # 解析执行人ID列表
        assigned_users = []
        if self.assigned_user_ids:
            try:
                assigned_users = json.loads(self.assigned_user_ids)
            except:
                assigned_users = []
        
        # 解析周期条目ID列表
        cycle_items = []
        if self.cycle_item_ids:
            try:
                cycle_items = json.loads(self.cycle_item_ids)
            except:
                cycle_items = []
                
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'group_code': self.group_code,
            'cycle_number': self.cycle_number,
            'cycle_type': self.cycle_type,
            'detection_category': self.detection_category,
            'point_name': self.point_name,
            'assigned_user_ids': assigned_users,
            'cycle_item_ids': cycle_items,
            'status': self.status,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
