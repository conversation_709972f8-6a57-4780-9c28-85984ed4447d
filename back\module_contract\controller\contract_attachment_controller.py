"""
合同附件控制器
"""

from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Depends, Request, Path as PathParam, Query, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_contract.entity.vo.contract_attachment_vo import (
    ContractAttachmentListModel,
    ContractAttachmentQueryModel,
)
from module_contract.service.contract_attachment_service import ContractAttachmentService
from utils.response_util import ResponseUtil

# 创建路由
contract_attachment = APIRouter(prefix="/contract/attachment", tags=["合同附件管理"])


@contract_attachment.post("/upload", response_model=CrudResponseModel, summary="上传合同附件")
async def upload_contract_attachments(
    request: Request,
    contract_id: int = Form(..., description="合同ID"),
    file: UploadFile = File(..., description="上传的文件列表"),
    remark: Optional[str] = Form(None, description="备注"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    上传合同附件

    :param request: 请求对象
    :param contract_id: 合同ID
    :param files: 上传的文件列表
    :param remark: 备注
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 上传结果
    """
    service = ContractAttachmentService(db)
    result = await service.upload_attachments(contract_id, [file], current_user, remark)
    return ResponseUtil.success(data=result)


@contract_attachment.get("/list", response_model=ContractAttachmentListModel, summary="获取合同附件列表")
async def get_contract_attachment_list(
    request: Request,
    contract_id: int = Query(..., description="合同ID"),
    original_filename: Optional[str] = Query(None, description="原始文件名"),
    file_type: Optional[str] = Query(None, description="文件类型"),
    page_num: Optional[int] = Query(1, description="页码"),
    page_size: Optional[int] = Query(10, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取合同附件列表

    :param request: 请求对象
    :param contract_id: 合同ID
    :param original_filename: 原始文件名
    :param file_type: 文件类型
    :param page_num: 页码
    :param page_size: 每页数量
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 附件列表
    """
    query_params = ContractAttachmentQueryModel(
        contract_id=contract_id,
        original_filename=original_filename,
        file_type=file_type,
        page_num=page_num,
        page_size=page_size,
    )
    service = ContractAttachmentService(db)
    attachment_list = await service.get_attachment_list(query_params)
    return ResponseUtil.success(data=attachment_list)


@contract_attachment.delete("/{attachment_id}", response_model=CrudResponseModel, summary="删除合同附件")
async def delete_contract_attachment(
    request: Request,
    attachment_id: int = PathParam(..., description="附件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除合同附件

    :param request: 请求对象
    :param attachment_id: 附件ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = ContractAttachmentService(db)
    result = await service.delete_attachment(attachment_id, current_user)
    return ResponseUtil.success(data=result)


@contract_attachment.get("/download/{attachment_id}", summary="下载合同附件")
async def download_contract_attachment(
    request: Request,
    attachment_id: int = PathParam(..., description="附件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    下载合同附件

    :param request: 请求对象
    :param attachment_id: 附件ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 文件响应
    """
    service = ContractAttachmentService(db)
    file_path, original_filename, file_type = await service.download_attachment(attachment_id)
    
    return FileResponse(
        path=file_path,
        filename=original_filename,
        media_type=file_type,
        headers={"Content-Disposition": f"attachment; filename*=UTF-8''{original_filename}"}
    )
