-- 质控样类型字典数据配置
-- 创建时间：2025-01-26

-- 1. 质控样类型字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('质控样类型', 'quality_control_type', '0', 'admin', NOW(), '', NULL, '质控样类型列表');

SET @quality_control_type_dict_id = LAST_INSERT_ID();

-- 质控样类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '平行样', 'parallel_sample', 'quality_control_type', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '平行样品，用于检验采样和分析的精密度'),
(2, '全程空白样', 'full_blank_sample', 'quality_control_type', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '全程空白样品，用于检验整个采样过程的污染情况'),
(3, '运输空白样', 'transport_blank_sample', 'quality_control_type', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '运输空白样品，用于检验运输过程的污染情况'),
(4, '设备清洗空白样', 'equipment_blank_sample', 'quality_control_type', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '设备清洗空白样品，用于检验设备清洗效果'),
(5, '基体加标样', 'matrix_spike_sample', 'quality_control_type', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '基体加标样品，用于检验基体效应和回收率'),
(6, '实验室平行样', 'lab_parallel_sample', 'quality_control_type', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '实验室平行样品，用于检验实验室分析的精密度');

-- 2. 样品类型字典（如果不存在的话）
INSERT IGNORE INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('样品类型', 'sample_type', '0', 'admin', NOW(), '', NULL, '样品类型列表');

-- 样品类型字典数据
INSERT IGNORE INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '正常样品', 'normal_sample', 'sample_type', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '正常采集的样品'),
(2, '质控样品', 'quality_control_sample', 'sample_type', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '质量控制样品');
