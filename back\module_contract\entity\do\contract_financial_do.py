"""
合同财务相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class ContractInvoice(Base):
    """
    合同开票记录表
    """

    __tablename__ = "contract_invoice"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    invoice_amount = Column(Numeric(15, 2), nullable=False, comment="开票金额")
    invoice_date = Column(Date, nullable=False, comment="开票日期")
    invoice_number = Column(String(100), nullable=True, comment="发票号码")
    invoice_type = Column(String(50), nullable=True, comment="发票类型")
    tax_rate = Column(Numeric(5, 2), nullable=True, comment="税率(%)")
    tax_amount = Column(Numeric(15, 2), nullable=True, comment="税额")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 关联关系
    contract = relationship("Contract", back_populates="invoices")


class ContractPayment(Base):
    """
    合同回款记录表
    """

    __tablename__ = "contract_payment"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    payment_amount = Column(Numeric(15, 2), nullable=False, comment="回款金额")
    payment_date = Column(Date, nullable=False, comment="回款日期")
    payment_method = Column(String(50), nullable=True, comment="回款方式")
    payment_account = Column(String(100), nullable=True, comment="回款账户")
    bank_info = Column(String(200), nullable=True, comment="银行信息")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 关联关系
    contract = relationship("Contract", back_populates="payments")


class ContractReceipt(Base):
    """
    合同收票记录表
    """

    __tablename__ = "contract_receipt"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    receipt_amount = Column(Numeric(15, 2), nullable=False, comment="收票金额")
    receipt_date = Column(Date, nullable=False, comment="收票日期")
    receipt_number = Column(String(100), nullable=True, comment="票据号码")
    receipt_type = Column(String(50), nullable=True, comment="票据类型")
    supplier_name = Column(String(200), nullable=True, comment="供应商名称")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 关联关系
    contract = relationship("Contract", back_populates="receipts")


class ContractCostPayment(Base):
    """
    合同付款记录表
    """

    __tablename__ = "contract_cost_payment"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    payment_amount = Column(Numeric(15, 2), nullable=False, comment="付款金额")
    payment_date = Column(Date, nullable=False, comment="付款日期")
    payment_method = Column(String(50), nullable=True, comment="付款方式")
    payee_name = Column(String(200), nullable=True, comment="收款方名称")
    payee_account = Column(String(100), nullable=True, comment="收款账户")
    bank_info = Column(String(200), nullable=True, comment="银行信息")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 关联关系
    contract = relationship("Contract", back_populates="cost_payments")
