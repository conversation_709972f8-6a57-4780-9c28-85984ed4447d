<template>
  <span>
    <!-- 批量导入按钮 -->
    <el-button type="success" @click="openImportDialog">
      <el-icon><Upload /></el-icon>
      批量导入检测项目
    </el-button>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入项目报价明细"
      width="600px"
      :close-on-click-modal="false"
      @close="resetImportDialog"
    >
      <div class="import-content">
        <!-- 开始导入区域 -->
        <div class="upload-section">
          <h4>开始导入</h4>
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :limit="1"
            :accept="'.xlsx,.xls'"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 下载模板区域 -->
        <div class="template-section">
          <h4>下载Excel模板</h4>
          <p class="template-description">
            请先下载模板，按照模板格式填写数据后再上传
          </p>
          <el-button type="success" @click="downloadTemplate" :loading="templateLoading">
            <el-icon><Download /></el-icon>
            下载模板
          </el-button>
          
          <!-- 模板字段说明 -->
          <div class="template-fields">
            <h5>模板字段说明：</h5>
            <ul>
              <li><strong>分类</strong>：检测分类（如：气、水、土壤等）</li>
              <li><strong>二级分类</strong>：具体检测类别（必填）</li>
              <li><strong>指标</strong>：检测指标名称（必填）</li>
              <li><strong>方法</strong>：检测方法描述（必填）</li>
              <li><strong>样品来源</strong>：样品来源描述</li>
              <li><strong>点位名称</strong>：采样点位名称</li>
              <li><strong>点位数</strong>：点位数量（数字）</li>
              <li><strong>周期类型</strong>：检测周期类型</li>
              <li><strong>检测周期数</strong>：检测周期数量（数字）</li>
              <li><strong>检测频次数</strong>：检测频次数量（数字）</li>
              <li><strong>样品数</strong>：样品数量（数字）</li>
              <li><strong>分包方式</strong>：填写"不分包、"分包"或"分包检测"（必填）</li>
              <li><strong>备注</strong>：备注信息</li>
            </ul>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport" :loading="importing" :disabled="!selectedFile">
            开始导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDialogVisible"
      title="导入错误详情"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="error-content">
        <div class="error-summary">
          <el-alert
            :title="`导入完成：总计 ${importResult.totalCount} 条，成功 ${importResult.successCount} 条，失败 ${importResult.errorCount} 条`"
            :type="importResult.errorCount > 0 ? 'warning' : 'success'"
            show-icon
            :closable="false"
          />
        </div>

        <div v-if="importResult.errorCount > 0" class="error-table">
          <h4>错误明细：</h4>
          <el-table :data="importResult.errors" style="width: 100%" border max-height="400">
            <el-table-column label="错误行" prop="rowNumber" width="80" align="center" />
            <el-table-column label="错误信息" prop="errorMessage" width="200">
              <template #default="scope">
                <el-tooltip :content="scope.row.errorMessage" placement="top">
                  <div class="error-message">{{ scope.row.errorMessage }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="分类" prop="classification" width="100" />
            <el-table-column label="二级分类" prop="category" width="120" />
            <el-table-column label="指标" prop="parameter" width="120" />
            <el-table-column label="方法" prop="method" width="150">
              <template #default="scope">
                <el-tooltip :content="scope.row.method" placement="top">
                  <div class="method-text">{{ scope.row.method }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="样品来源" prop="sampleSource" width="120" />
            <el-table-column label="点位名称" prop="pointName" width="120" />
            <el-table-column label="点位数" prop="pointCount" width="80" />
            <el-table-column label="周期类型" prop="cycleType" width="100" />
            <el-table-column label="检测周期数" prop="cycleCount" width="100" />
            <el-table-column label="检测频次数" prop="frequency" width="100" />
            <el-table-column label="样品数" prop="sampleCount" width="80" />
            <el-table-column label="分包方式" prop="subcontractMethod" width="120" />
            <el-table-column label="备注" prop="remark" min-width="150">
              <template #default="scope">
                <el-tooltip :content="scope.row.remark" placement="top">
                  <div class="remark-text">{{ scope.row.remark }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="confirmImport" v-if="importResult.successCount > 0">
            确认导入成功数据
          </el-button>
          <el-button @click="errorDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </span>
</template>

<script setup>
import { ref, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, UploadFilled, Download } from '@element-plus/icons-vue'
import { downloadProjectQuotationItemsImportTemplate, importProjectQuotationItems } from '@/api/quotation/projectQuotation'

// 定义事件
const emit = defineEmits(['import-success'])

// 响应式数据
const importDialogVisible = ref(false)
const errorDialogVisible = ref(false)
const templateLoading = ref(false)
const importing = ref(false)
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)

// 导入结果
const importResult = ref({
  success: false,
  totalCount: 0,
  successCount: 0,
  errorCount: 0,
  errors: [],
  data: []
})

// 打开导入对话框
function openImportDialog() {
  importDialogVisible.value = true
}

// 重置导入对话框
function resetImportDialog() {
  fileList.value = []
  selectedFile.value = null
  importing.value = false
  uploadRef.value?.clearFiles()
}

// 处理文件选择
function handleFileChange(file) {
  selectedFile.value = file.raw
}

// 处理文件数量超限
function handleExceed() {
  ElMessage.warning('只能选择一个文件进行导入')
}

// 下载模板
async function downloadTemplate() {
  try {
    templateLoading.value = true
    const response = await downloadProjectQuotationItemsImportTemplate()
    
    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '项目报价明细导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  } finally {
    templateLoading.value = false
  }
}

// 开始导入
async function startImport() {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    importing.value = true
    const response = await importProjectQuotationItems(selectedFile.value)
    
    importResult.value = response.data
    
    if (importResult.value.success) {
      ElMessage.success(`导入成功！共导入 ${importResult.value.successCount} 条记录`)
      importDialogVisible.value = false
      emit('import-success', importResult.value.data)
    } else {
      if (importResult.value.errorCount > 0) {
        // 有错误，显示错误详情
        errorDialogVisible.value = true
        importDialogVisible.value = false
      } else {
        ElMessage.error('导入失败，请检查文件格式')
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    if (error.response && error.response.data && error.response.data.message) {
      ElMessage.error(`导入失败：${error.response.data.message}`)
    } else {
      ElMessage.error('导入失败，请检查文件格式和网络连接')
    }
  } finally {
    importing.value = false
  }
}

// 确认导入成功数据
function confirmImport() {
  if (importResult.value.successCount > 0) {
    emit('import-success', importResult.value.data)
    ElMessage.success(`已导入 ${importResult.value.successCount} 条成功数据`)
  }
  errorDialogVisible.value = false
}
</script>

<style scoped>
.import-content {
  padding: 20px 0;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.upload-dragger {
  width: 100%;
}

.template-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.template-description {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.template-fields {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.template-fields h5 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.template-fields ul {
  margin: 0;
  padding-left: 20px;
}

.template-fields li {
  margin-bottom: 8px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.error-content {
  padding: 10px 0;
}

.error-summary {
  margin-bottom: 20px;
}

.error-table h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 600;
}

.error-message,
.method-text,
.remark-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
