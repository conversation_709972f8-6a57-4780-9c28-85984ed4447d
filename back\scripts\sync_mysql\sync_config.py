#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库同步配置文件
使用示例配置，用户可以根据实际情况修改
"""

# 源数据库配置
SOURCE_DB_CONFIG = {
    'host': '***********',  # 源数据库主机地址
    'port': 3306,             # 源数据库端口
    'username': 'lims-user',       # 源数据库用户名
    'password': 'lims-Root1',   # 源数据库密码
    'database': 'lims'   # 源数据库名
}

# 目标数据库配置
TARGET_DB_CONFIG = {
    'host': '127.0.0.1',      # 目标数据库主机地址
    'port': 3306,             # 目标数据库端口
    'username': 'lims-user',  # 目标数据库用户名
    'password': 'lims-Root1', # 目标数据库密码
    'database': 'lims'        # 目标数据库名
}

# 同步选项
SYNC_OPTIONS = {
    'batch_size': 1000,       # 批量插入大小
    'enable_foreign_key_check': True,  # 是否启用外键检查
    'backup_before_sync': False,         # 同步前是否备份目标数据库
    'exclude_tables': [],               # 排除的表名列表
    'include_tables': [],               # 仅同步的表名列表（为空则同步所有表）
}