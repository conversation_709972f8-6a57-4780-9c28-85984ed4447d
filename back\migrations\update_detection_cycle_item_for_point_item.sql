-- 更新检测周期条目表结构，改为基于点位明细生成
-- 替换项目报价明细关联为点位明细关联

-- 1. 添加新字段
ALTER TABLE detection_cycle_item
ADD COLUMN project_quotation_item_point_item_id INT NOT NULL COMMENT '项目报价明细点位项目ID' AFTER project_quotation_item_id;

-- 2. 添加外键约束
ALTER TABLE detection_cycle_item
ADD CONSTRAINT fk_detection_cycle_item_point_item
FOREIGN KEY (project_quotation_item_point_item_id)
REFERENCES project_quotation_item_point_item(id)
ON DELETE CASCADE;

-- 3. 添加索引
CREATE INDEX idx_detection_cycle_item_point_item_id ON detection_cycle_item(project_quotation_item_point_item_id);

-- 4. 修改唯一约束，改为基于点位明细的唯一性
-- 先删除原有约束
ALTER TABLE detection_cycle_item DROP CONSTRAINT uk_quotation_item_cycle;

-- 添加新的唯一约束，确保在同一个点位明细下周期号唯一
ALTER TABLE detection_cycle_item
ADD CONSTRAINT uk_point_item_cycle
UNIQUE (project_quotation_item_point_item_id, cycle_number);

-- 5. 删除原有的项目报价明细关联字段（数据迁移后）
-- 注意：在生产环境中，需要先迁移数据，然后再删除字段
-- ALTER TABLE detection_cycle_item DROP COLUMN project_quotation_item_id;

-- 6. 更新表注释
ALTER TABLE detection_cycle_item COMMENT = '检测周期条目表 - 基于项目报价明细点位项目生成周期条目';
