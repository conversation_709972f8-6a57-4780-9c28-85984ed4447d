/**
 * 中文编码处理模块
 * 解决小程序蓝牙打印中文乱码问题
 */

// 引入text-encoding库进行GB2312编码
const TextEncoder = require('../miniprogram_npm/text-encoding/index').TextEncoder;

class ChineseTextEncoder {
  constructor() {
    // 使用GB2312编码处理中文字符
    this.encoder = new TextEncoder("gb2312", {
      NONSTANDARD_allowLegacyEncoding: true
    });
  }

  /**
   * 将字符串编码为GB2312字节数组
   * @param {string} text - 要编码的文本
   * @returns {Array} 编码后的字节数组
   */
  encode(text) {
    if (!text) return [];
    
    try {
      // 使用GB2312编码
      const uint8Array = this.encoder.encode(text);
      return Array.from(uint8Array);
    } catch (error) {
      console.error('文本编码失败:', error);
      // 降级处理：使用UTF-8编码
      return this.fallbackEncode(text);
    }
  }

  /**
   * 降级编码方案（UTF-8）
   * @param {string} text - 要编码的文本
   * @returns {Array} 编码后的字节数组
   */
  fallbackEncode(text) {
    const bytes = [];
    for (let i = 0; i < text.length; i++) {
      const code = text.charCodeAt(i);
      if (code < 0x80) {
        // ASCII字符
        bytes.push(code);
      } else if (code < 0x800) {
        // 双字节UTF-8
        bytes.push(0xC0 | (code >> 6));
        bytes.push(0x80 | (code & 0x3F));
      } else {
        // 三字节UTF-8
        bytes.push(0xE0 | (code >> 12));
        bytes.push(0x80 | ((code >> 6) & 0x3F));
        bytes.push(0x80 | (code & 0x3F));
      }
    }
    return bytes;
  }

  /**
   * 检测字符串是否包含中文
   * @param {string} str - 要检测的字符串
   * @returns {boolean} 是否包含中文
   */
  hasChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
  }

  /**
   * 获取字符串的显示宽度（中文=2，英文=1）
   * @param {string} str - 要计算的字符串
   * @returns {number} 显示宽度
   */
  getDisplayWidth(str) {
    let width = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      // 中文字符宽度为2，英文字符宽度为1
      width += /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
    }
    return width;
  }

  /**
   * 按显示宽度截取字符串
   * @param {string} str - 原字符串
   * @param {number} maxWidth - 最大显示宽度
   * @returns {string} 截取后的字符串
   */
  truncateByWidth(str, maxWidth) {
    let width = 0;
    let result = '';
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      const charWidth = /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
      
      if (width + charWidth > maxWidth) {
        break;
      }
      
      width += charWidth;
      result += char;
    }
    
    return result;
  }
}

module.exports = ChineseTextEncoder;
