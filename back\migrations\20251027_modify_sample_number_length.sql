-- 修改sample_record表的sample_number字段类型和长度
-- 日期: 2025-10-27
-- 说明: 将sample_number字段从INT修改为VARCHAR(100)，以支持新的样品编号格式
--       旧格式: 整数序号（1, 2, 3...）
--       新格式: 任务单号(8位) + 类别标识(1-2位) + 样品序号(3位) = 最多13位
--       例如: 25100008S001 (水样品)
--             25100008GG001 (公共场所类别标识为GG，共13位)
--
-- 注意: 此修改会将现有的整数值转换为字符串
--       例如: 1 -> '1', 2 -> '2'

-- 修改字段类型和长度
ALTER TABLE sample_record MODIFY COLUMN sample_number VARCHAR(100) NOT NULL COMMENT '样品编号（格式：任务单号+类别标识+样品序号）';

