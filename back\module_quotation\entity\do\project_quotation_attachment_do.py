"""
项目报价附件数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationAttachment(Base):
    """
    项目报价附件表
    """
    __tablename__ = 'project_quotation_attachment'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    file_name = Column(String(100), nullable=False, comment='文件名称')
    file_path = Column(String(200), nullable=False, comment='文件路径')
    file_size = Column(Integer, nullable=False, comment='文件大小')
    file_type = Column(String(50), nullable=True, comment='文件类型')
    
    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment='创建人')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(50), nullable=True, comment='更新人')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    
    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="attachments")
