from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


def add_cors_middleware(app: FastAPI):
    """
    添加跨域中间件

    :param app: FastAPI对象
    :return:
    """
    # 前端页面url - 支持局域网访问
    origins = [
        'http://localhost:80',
        'http://127.0.0.1:80',
        'http://localhost:4000',
        'http://127.0.0.1:4000',
        'http://************:4000',  # 局域网IP
        'http://192.168.1.*:4000',   # 局域网段
        '*'  # 开发环境允许所有来源
    ]

    # 后台api允许跨域
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],  # 开发环境允许所有来源
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
