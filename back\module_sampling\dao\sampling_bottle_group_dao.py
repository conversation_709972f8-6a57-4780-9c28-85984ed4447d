"""
采样瓶组数据访问对象
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import joinedload, selectinload

from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_sample_do import SamplingBottleGroupSample
from module_sampling.entity.do.sampling_bottle_group_sequence_do import SamplingBottleGroupSequence
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance



class SamplingBottleGroupDAO:
    """采样瓶组数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_bottle_group(self, bottle_group: SamplingBottleGroup) -> SamplingBottleGroup:
        """创建瓶组记录"""
        self.db.add(bottle_group)
        await self.db.flush()
        await self.db.refresh(bottle_group)
        return bottle_group
    
    async def batch_create_bottle_groups(self, bottle_groups: List[SamplingBottleGroup]) -> List[SamplingBottleGroup]:
        """批量创建瓶组记录"""
        self.db.add_all(bottle_groups)
        await self.db.flush()
        for group in bottle_groups:
            await self.db.refresh(group)
        return bottle_groups
    
    async def get_bottle_group_by_id(self, bottle_group_id: int) -> Optional[SamplingBottleGroup]:
        """根据ID获取瓶组记录"""
        stmt = select(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_bottle_groups_by_task_id(self, task_id: int) -> List[SamplingBottleGroup]:
        """根据任务ID获取瓶组列表"""
        stmt = select(SamplingBottleGroup).where(SamplingBottleGroup.sampling_task_id == task_id).order_by(SamplingBottleGroup.bottle_group_code)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_groups_with_details_by_task_id(self, task_id: int) -> List[Dict[str, Any]]:
        """根据任务ID获取瓶组详情列表（包含瓶组信息和样品信息）"""
        stmt = select(
            SamplingBottleGroup,
            BottleMaintenance.bottle_type,
            BottleMaintenance.bottle_volume,
            BottleMaintenance.storage_styles,
            BottleMaintenance.fix_styles,
            BottleMaintenance.sample_age,
            BottleMaintenance.sample_age_unit
        ).outerjoin(
            BottleMaintenance, 
            and_(
                SamplingBottleGroup.bottle_maintenance_id == BottleMaintenance.id,
                SamplingBottleGroup.bottle_maintenance_id != 0
            )
        ).where(SamplingBottleGroup.sampling_task_id == task_id).order_by(SamplingBottleGroup.bottle_group_code)
        
        result = await self.db.execute(stmt)
        rows = result.fetchall()
        
        bottle_groups = []
        for row in rows:
            bottle_group = row.SamplingBottleGroup

            # 解析JSON字段
            storage_styles = []
            fix_styles = []

            if row.storage_styles:
                try:
                    import json
                    if isinstance(row.storage_styles, str):
                        storage_styles = json.loads(row.storage_styles)
                    elif isinstance(row.storage_styles, list):
                        storage_styles = row.storage_styles
                except (json.JSONDecodeError, TypeError):
                    storage_styles = []

            if row.fix_styles:
                try:
                    import json
                    if isinstance(row.fix_styles, str):
                        fix_styles = json.loads(row.fix_styles)
                    elif isinstance(row.fix_styles, list):
                        fix_styles = row.fix_styles
                except (json.JSONDecodeError, TypeError):
                    fix_styles = []

            bottle_group_data = {
                'id': bottle_group.id,
                'sampling_task_id': bottle_group.sampling_task_id,
                'bottle_group_code': bottle_group.bottle_group_code,
                'bottle_maintenance_id': bottle_group.bottle_maintenance_id,
                'detection_method': bottle_group.detection_method,
                'sample_count': bottle_group.sample_count,
                'status': bottle_group.status,
                'create_time': bottle_group.create_time,
                'update_time': bottle_group.update_time,
                'bottle_type': row.bottle_type if row.bottle_type else '默认瓶组',
                'bottle_volume': row.bottle_volume if row.bottle_volume else '-',
                'storage_styles': storage_styles,
                'fix_styles': fix_styles,
                'sample_age': row.sample_age,
                'sample_age_unit': row.sample_age_unit
            }
            bottle_groups.append(bottle_group_data)
        
        return bottle_groups
    
    async def update_bottle_group(self, bottle_group: SamplingBottleGroup) -> SamplingBottleGroup:
        """更新瓶组记录"""
        await self.db.flush()
        await self.db.refresh(bottle_group)
        return bottle_group
    
    async def update_bottle_group_status(self, bottle_group_id: int, status: int, update_by: int) -> bool:
        """更新瓶组状态"""
        stmt = update(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id).values(
            status=status,
            update_by=update_by
        )
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_group(self, bottle_group_id: int) -> bool:
        """删除瓶组记录"""
        stmt = delete(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def get_bottle_groups_by_bottle_maintenance_id(self, bottle_maintenance_id: int) -> List[SamplingBottleGroup]:
        """根据瓶组管理ID获取瓶组列表"""
        stmt = select(SamplingBottleGroup).where(SamplingBottleGroup.bottle_maintenance_id == bottle_maintenance_id)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def count_bottle_groups_by_task_id(self, task_id: int) -> int:
        """统计任务下的瓶组数量"""
        stmt = select(func.count(SamplingBottleGroup.id)).where(SamplingBottleGroup.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    

    
    async def get_next_bottle_group_sequence(self, task_id: int) -> int:
        """获取下一个瓶组序号"""
        # 查找现有序列记录
        stmt = select(SamplingBottleGroupSequence).where(SamplingBottleGroupSequence.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        sequence_record = result.scalar_one_or_none()

        if sequence_record:
            # 更新序列号
            sequence_record.current_sequence += 1
            await self.db.flush()
            return sequence_record.current_sequence
        else:
            # 创建新的序列记录
            new_sequence = SamplingBottleGroupSequence(
                sampling_task_id=task_id,
                current_sequence=1
            )
            self.db.add(new_sequence)
            await self.db.flush()
            return 1

    async def update_bottle_group_sample_count(self, bottle_group_id: int, sample_count: int) -> bool:
        """更新瓶组的样品数量"""
        stmt = update(SamplingBottleGroup).where(
            SamplingBottleGroup.id == bottle_group_id
        ).values(sample_count=sample_count)

        result = await self.db.execute(stmt)
        await self.db.flush()
        return result.rowcount > 0
