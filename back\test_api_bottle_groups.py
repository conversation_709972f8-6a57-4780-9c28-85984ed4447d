"""
通过API测试质控样瓶组复制功能
"""
import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_quality_control_bottle_groups():
    """测试质控样瓶组复制"""
    print("=" * 80)
    print("通过API测试质控样瓶组复制功能")
    print("=" * 80)
    
    # 1. 查找样品1的瓶组
    print("\n1. 查找样品1的瓶组...")
    original_sample_id = 1
    
    response = requests.get(
        f"{BASE_URL}/sampling/bottle-groups/sample/{original_sample_id}",
        headers=HEADERS
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['code'] == 200:
            original_bottle_groups = result['data']
            print(f"   ✅ 样品 {original_sample_id} 关联了 {len(original_bottle_groups)} 个瓶组:")
            for bg in original_bottle_groups:
                print(f"      - 瓶组编号: {bg['bottleGroupCode']}, 瓶组ID: {bg['id']}, 样品数: {bg['sampleCount']}")
            
            if len(original_bottle_groups) == 0:
                print("   ❌ 样品没有瓶组关联，请先创建瓶组")
                return
        else:
            print(f"   ❌ 获取瓶组失败: {result.get('msg', '未知错误')}")
            return
    else:
        print(f"   ❌ API请求失败: {response.status_code}")
        return
    
    # 2. 创建质控样
    print(f"\n2. 为样品 {original_sample_id} 创建质控样...")
    
    create_data = {
        "originalSampleId": original_sample_id,
        "qualityControlTypes": ["parallel_sample", "full_blank_sample"]
    }
    
    response = requests.post(
        f"{BASE_URL}/sampling/sample-records/quality-control/create",
        headers=HEADERS,
        json=create_data
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['code'] == 200:
            created_samples = result['data']
            print(f"   ✅ 成功创建 {len(created_samples)} 个质控样:")
            for sample in created_samples:
                print(f"      - 样品ID: {sample['id']}, 样品编号: {sample['sampleNumber']}, 类型: {sample['qualityControlType']}")
        else:
            print(f"   ❌ 创建质控样失败: {result.get('msg', '未知错误')}")
            return
    else:
        print(f"   ❌ API请求失败: {response.status_code}")
        print(f"   响应内容: {response.text}")
        return
    
    # 3. 检查质控样的瓶组关联
    print(f"\n3. 检查质控样的瓶组关联...")
    for sample in created_samples:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{sample['id']}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['code'] == 200:
                qc_bottle_groups = result['data']
                print(f"   质控样 {sample['id']} (样品编号: {sample['sampleNumber']}) 关联了 {len(qc_bottle_groups)} 个瓶组:")
                
                if len(qc_bottle_groups) == len(original_bottle_groups):
                    print(f"      ✅ 瓶组数量匹配 ({len(qc_bottle_groups)} 个)")
                else:
                    print(f"      ❌ 瓶组数量不匹配! 原样品: {len(original_bottle_groups)}, 质控样: {len(qc_bottle_groups)}")
                
                for bg in qc_bottle_groups:
                    print(f"      - 瓶组编号: {bg['bottleGroupCode']}, 瓶组ID: {bg['id']}, 样品数: {bg['sampleCount']}")
            else:
                print(f"   ❌ 获取瓶组失败: {result.get('msg', '未知错误')}")
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
    
    # 4. 验证瓶组的样品数量是否更新
    print(f"\n4. 验证瓶组的样品数量是否更新...")
    for bg in original_bottle_groups:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{original_sample_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['code'] == 200:
                updated_bottle_groups = result['data']
                for updated_bg in updated_bottle_groups:
                    if updated_bg['id'] == bg['id']:
                        expected_count = bg['sampleCount'] + len(created_samples)
                        actual_count = updated_bg['sampleCount']
                        
                        if actual_count == expected_count:
                            print(f"   ✅ 瓶组 {bg['bottleGroupCode']} 样品数量已更新: {bg['sampleCount']} -> {actual_count}")
                        else:
                            print(f"   ⚠️  瓶组 {bg['bottleGroupCode']} 样品数量: {bg['sampleCount']} -> {actual_count} (期望: {expected_count})")
                        break
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)


if __name__ == "__main__":
    test_quality_control_bottle_groups()

