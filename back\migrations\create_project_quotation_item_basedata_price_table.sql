-- 创建项目报价明细基础价目表
-- 用于存储项目报价中每个类别-方法组合对应的价格信息

CREATE TABLE `project_quotation_item_basedata_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_quotation_id` int(11) NOT NULL COMMENT '项目报价ID',
  `project_quotation_item_id` varchar(20) NOT NULL COMMENT '项目明细的ID',
  
  -- 以下字段完全来源于 technical_manual_price 表
  `method` varchar(255) NOT NULL COMMENT '检测方法',
  `category` varchar(50) NOT NULL COMMENT '检测类别',
  `classification` varchar(50) DEFAULT NULL COMMENT '分类',
  `first_item_price` decimal(10,2) DEFAULT NULL COMMENT '检测首项单价',
  `additional_item_price` decimal(10,2) DEFAULT NULL COMMENT '检测增项单价',
  `testing_fee_limit` decimal(10,2) DEFAULT NULL COMMENT '检测费上限',
  `sampling_price` decimal(10,2) DEFAULT NULL COMMENT '采集单价',
  `pretreatment_price` decimal(10,2) DEFAULT NULL COMMENT '前处理单价',
  `special_consumables_price` decimal(10,2) DEFAULT NULL COMMENT '分析特殊耗材单价',
  
  -- 基础字段
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  KEY `idx_project_quotation_id` (`project_quotation_id`),
  KEY `idx_category_method` (`category`, `method`),
  KEY `idx_project_quotation_item_id` (`project_quotation_item_id`),
  CONSTRAINT `fk_project_quotation_item_basedata_price_quotation` 
    FOREIGN KEY (`project_quotation_id`) REFERENCES `project_quotation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='项目报价明细基础价目表';

-- 添加索引以提高查询性能
CREATE INDEX idx_project_quotation_item_basedata_price_classification ON project_quotation_item_basedata_price(classification);
CREATE INDEX idx_project_quotation_item_basedata_price_create_time ON project_quotation_item_basedata_price(create_time);

-- 插入示例数据（可选）
-- INSERT INTO project_quotation_item_basedata_price (
--   project_quotation_id, project_quotation_item_id, method, category, classification,
--   first_item_price, additional_item_price, testing_fee_limit, 
--   sampling_price, pretreatment_price, special_consumables_price,
--   create_by, create_time, remark
-- ) VALUES (
--   1, 'SAMPLE_001', '环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013', 
--   '环境空气和废气', '气', 
--   100.00, 50.00, 500.00, 
--   30.00, 20.00, 10.00,
--   'system', NOW(), '示例数据'
-- );
