# 点位确认组件测试指南

## 手动测试步骤

### 1. 基础功能测试

#### 1.1 打开点位确认弹窗
- 在任务拆解列表页面，点击任意项目的"点位确认"按钮
- 验证弹窗正常打开，显示该项目的所有点位明细

#### 1.2 点位列表显示
- 验证点位列表正确显示以下信息：
  - 点位名称
  - 确认状态（已确认/未确认）
  - 确认人
  - 确认时间
  - 操作按钮（重命名、确认/取消确认）

#### 1.3 统计信息
- 验证底部统计栏正确显示：
  - 总计点位数
  - 已确认点位数
  - 未确认点位数

### 2. 点位重命名功能测试

#### 2.1 开始重命名
- 点击任意点位的"重命名"按钮
- 验证点位名称变为可编辑状态
- 验证输入框自动聚焦

#### 2.2 保存重命名
- 修改点位名称后按回车或点击"保存"按钮
- 验证名称更新成功
- 验证显示成功提示消息

#### 2.3 取消重命名
- 开始重命名后按ESC键或点击"取消"按钮
- 验证恢复原始名称
- 验证退出编辑状态

#### 2.4 重命名验证
- 尝试保存空名称，验证显示警告提示
- 尝试保存相同名称，验证直接退出编辑状态

### 3. 点位确认功能测试

#### 3.1 单个点位确认
- 点击未确认点位的"确认"按钮
- 验证状态变为"已确认"
- 验证确认人和确认时间正确显示
- 验证显示成功提示消息

#### 3.2 单个点位取消确认
- 点击已确认点位的"取消确认"按钮
- 验证状态变为"未确认"
- 验证确认人和确认时间清空
- 验证显示成功提示消息

#### 3.3 批量确认
- 选择多个未确认的点位
- 点击"批量确认"按钮
- 确认操作提示弹窗
- 验证所有选中点位状态变为"已确认"
- 验证显示成功提示消息

#### 3.4 批量取消确认
- 选择多个已确认的点位
- 点击"批量取消确认"按钮
- 确认操作提示弹窗
- 验证所有选中点位状态变为"未确认"
- 验证显示成功提示消息

### 4. 交互功能测试

#### 4.1 选择功能
- 验证全选/取消全选功能正常
- 验证单个选择/取消选择功能正常
- 验证批量操作按钮状态根据选择情况正确启用/禁用

#### 4.2 刷新功能
- 点击"刷新"按钮
- 验证数据重新加载
- 验证选择状态被清空

#### 4.3 关闭弹窗
- 点击"关闭"按钮或弹窗外部区域
- 验证弹窗正常关闭
- 验证数据被清空

### 5. 错误处理测试

#### 5.1 网络错误
- 模拟网络断开情况
- 验证操作失败时显示错误提示
- 验证数据状态不会错误更新

#### 5.2 权限错误
- 模拟无权限情况
- 验证显示相应错误提示

#### 5.3 数据不存在
- 模拟点位明细不存在的情况
- 验证显示相应错误提示

### 6. 集成测试

#### 6.1 与任务拆解列表的集成
- 在任务拆解列表中点击"点位确认"按钮
- 完成点位确认操作后关闭弹窗
- 点击"采样分配"按钮
- 验证只显示已确认点位的检测周期条目

#### 6.2 与采样分配的集成
- 确认部分点位后进行采样分配
- 验证采样分配页面只显示已确认点位
- 验证显示相应提示信息

## 自动化测试建议

### 1. 单元测试
- 测试组件的各个方法
- 测试数据处理逻辑
- 测试状态管理

### 2. 集成测试
- 测试与API的交互
- 测试与父组件的通信
- 测试事件传递

### 3. E2E测试
- 测试完整的用户操作流程
- 测试跨页面的功能集成
- 测试错误场景的处理

## 测试数据准备

### 1. 项目报价数据
- 创建已审核的项目报价
- 包含多个点位明细
- 部分点位已确认，部分未确认

### 2. 用户权限
- 确保测试用户具有点位确认权限
- 测试无权限用户的访问限制

### 3. 网络环境
- 正常网络环境
- 网络延迟环境
- 网络断开环境

## 性能测试

### 1. 大数据量测试
- 测试包含大量点位明细的项目
- 验证列表渲染性能
- 验证批量操作性能

### 2. 并发测试
- 多用户同时操作同一项目的点位确认
- 验证数据一致性
- 验证冲突处理

## 浏览器兼容性测试

### 1. 主流浏览器
- Chrome
- Firefox
- Safari
- Edge

### 2. 移动端浏览器
- 移动端Chrome
- 移动端Safari

## 注意事项

1. 测试前确保后端服务正常运行
2. 测试数据应该覆盖各种边界情况
3. 注意测试数据的清理，避免影响其他测试
4. 记录测试过程中发现的问题和改进建议
