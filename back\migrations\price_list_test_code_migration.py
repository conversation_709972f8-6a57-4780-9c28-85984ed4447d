"""
价目表和技术手册表结构迁移脚本
添加test_code字段，并将现有数据关联起来
"""

import asyncio
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from config.database import SQLALCHEMY_DATABASE_URL
from module_basedata.entity.do.technical_manual_do import TechnicalManual

# 创建异步引擎和会话
engine = create_async_engine(SQLALCHEMY_DATABASE_URL)
async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)


async def migrate_data():
    """
    迁移数据：
    1. 为技术手册表中没有test_code的记录生成test_code
    2. 更新价目表中的test_code字段，关联到对应的技术手册记录
    """
    async with async_session() as session:
        try:
            # 1. 查询所有没有test_code的技术手册记录
            stmt = select(TechnicalManual).where(and_(TechnicalManual.test_code.is_(None)))
            result = await session.execute(stmt)
            technical_manuals = result.scalars().all()

            # 获取当前最大的test_code
            max_stmt = (
                select(TechnicalManual.test_code)
                .where(TechnicalManual.test_code.like("JC%"))
                .order_by(TechnicalManual.test_code.desc())
                .limit(1)
            )
            max_result = await session.execute(max_stmt)
            max_code = max_result.scalar()

            if max_code:
                try:
                    current_num = int(max_code[2:])
                except (ValueError, IndexError):
                    current_num = 0
            else:
                current_num = 0

            # 为每个没有test_code的技术手册记录生成test_code
            for manual in technical_manuals:
                current_num += 1
                test_code = f"JC{current_num:06d}"
                manual.test_code = test_code

            # 提交更改
            await session.commit()
            print(f"已为{len(technical_manuals)}条技术手册记录生成test_code")

            # 2. 检查价目表是否有category, parameter, method字段
            # 如果有这些字段，则更新test_code字段
            try:
                # 尝试查询价目表中的category, parameter, method字段
                stmt = """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'price_list'
                AND column_name IN ('category', 'parameter', 'method')
                """
                result = await session.execute(stmt)
                existing_columns = result.scalars().all()

                if existing_columns and len(existing_columns) == 3:
                    print("价目表中存在category, parameter, method字段，将进行数据迁移")

                    # 查询所有价目表记录
                    stmt = """
                    SELECT id, category, parameter, method
                    FROM price_list
                    WHERE 1 == 1
                    """
                    result = await session.execute(stmt)
                    price_lists = result.fetchall()

                    updated_count = 0
                    for price in price_lists:
                        price_id = price[0]
                        category = price[1]
                        parameter = price[2]
                        method = price[3]

                        # 查找匹配的技术手册记录
                        stmt = (
                            select(TechnicalManual)
                            .where(
                                and_(
                                    TechnicalManual.category == category,
                                    TechnicalManual.parameter == parameter,
                                    TechnicalManual.method == method,
                                )
                            )
                            .limit(1)
                        )
                        result = await session.execute(stmt)
                        manual = result.scalar()

                        if manual and manual.test_code:
                            # 更新价目表的test_code字段
                            update_stmt = f"""
                            UPDATE price_list
                            SET test_code = '{manual.test_code}'
                            WHERE id = {price_id}
                            """
                            await session.execute(update_stmt)
                            updated_count += 1
                else:
                    print("价目表中不存在category, parameter, method字段，无需数据迁移")
                    updated_count = 0
            except Exception as e:
                print(f"检查价目表字段时出错: {str(e)}")
                updated_count = 0

            # 提交更改
            await session.commit()
            print(f"已更新{updated_count}条价目表记录的test_code字段")

            return {"technical_manuals_updated": len(technical_manuals), "price_lists_updated": updated_count}
        except Exception as e:
            await session.rollback()
            print(f"迁移失败: {str(e)}")
            raise


async def main():
    """
    主函数
    """
    print("开始数据迁移...")
    result = await migrate_data()
    print(f"数据迁移完成: {result}")


if __name__ == "__main__":
    asyncio.run(main())
