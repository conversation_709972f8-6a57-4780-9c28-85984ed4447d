<template>
  <div class="contract-cost-management">
    <!-- 汇总信息 -->
    <el-card class="summary-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">成本汇总</span>
          
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="合同成本" :value="summaryData.contractCost" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已收票金额" :value="summaryData.totalReceiptAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已付款金额" :value="summaryData.totalCostPaymentAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="待付款金额" 
            :value="summaryData.pendingCostPaymentAmount" 
            :precision="2" 
            prefix="¥"
            :value-style="{ color: summaryData.pendingCostPaymentAmount > 0 ? '#f56c6c' : '#67c23a' }"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 收票记录 -->
    <el-card class="mt-4" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">收票记录</span>
          <el-button 
            v-if="!readonly" 
            type="primary" 
            size="small" 
            @click="showAddReceiptDialog"
          >
            新增收票
          </el-button>
        </div>
      </template>
      
      <el-table :data="receiptList" style="width: 100%" size="small">
        <el-table-column prop="receiptNumber" label="发票号码" width="150" />
        <el-table-column prop="receiptAmount" label="收票金额" width="120">
          <template #default="scope">
            ¥{{ Number(scope.row.receiptAmount).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="receiptDate" label="收票日期" width="120" />
        <el-table-column prop="receiptType" label="发票类型" width="100" />
        <el-table-column prop="supplierName" label="供应商" width="150" />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column v-if="!readonly" label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="deleteReceipt(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 付款记录 -->
    <el-card class="mt-4" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">付款记录</span>
          <el-button 
            v-if="!readonly" 
            type="primary" 
            size="small" 
            @click="showAddCostPaymentDialog"
          >
            新增付款
          </el-button>
        </div>
      </template>
      
      <el-table :data="costPaymentList" style="width: 100%" size="small">
        <el-table-column prop="paymentAmount" label="付款金额" width="120">
          <template #default="scope">
            ¥{{ Number(scope.row.paymentAmount).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentDate" label="付款日期" width="120" />
        <el-table-column prop="paymentMethod" label="付款方式" width="100" />
        <el-table-column prop="payeeName" label="收款人" width="120" />
        <el-table-column prop="payeeAccount" label="收款账户" width="150" />
        <el-table-column prop="bankInfo" label="银行信息" min-width="150" />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column v-if="!readonly" label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="deleteCostPayment(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增收票对话框 -->
    <el-dialog v-model="receiptDialogVisible" title="新增收票记录" width="600px">
      <el-form ref="receiptFormRef" :model="receiptForm" :rules="receiptRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发票号码" prop="receiptNumber">
              <el-input v-model="receiptForm.receiptNumber" placeholder="请输入发票号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收票金额" prop="receiptAmount">
              <el-input-number
                v-model="receiptForm.receiptAmount"
                :precision="2"
                :min="0"
                placeholder="收票金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收票日期" prop="receiptDate">
              <el-date-picker
                v-model="receiptForm.receiptDate"
                type="date"
                placeholder="选择收票日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票类型" prop="receiptType">
              <el-select v-model="receiptForm.receiptType" placeholder="请选择发票类型" style="width: 100%">
                <el-option label="增值税专用发票" value="增值税专用发票" />
                <el-option label="增值税普通发票" value="增值税普通发票" />
                <el-option label="电子发票" value="电子发票" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="供应商" prop="supplierName">
          <el-input v-model="receiptForm.supplierName" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="receiptForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="receiptDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitReceipt" :loading="receiptSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增付款对话框 -->
    <el-dialog v-model="costPaymentDialogVisible" title="新增付款记录" width="600px">
      <el-form ref="costPaymentFormRef" :model="costPaymentForm" :rules="costPaymentRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款金额" prop="paymentAmount">
              <el-input-number
                v-model="costPaymentForm.paymentAmount"
                :precision="2"
                :min="0"
                placeholder="付款金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款日期" prop="paymentDate">
              <el-date-picker
                v-model="costPaymentForm.paymentDate"
                type="date"
                placeholder="选择付款日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款方式" prop="paymentMethod">
              <el-select v-model="costPaymentForm.paymentMethod" placeholder="请选择付款方式" style="width: 100%">
                <el-option label="银行转账" value="银行转账" />
                <el-option label="现金" value="现金" />
                <el-option label="支票" value="支票" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人" prop="payeeName">
              <el-input v-model="costPaymentForm.payeeName" placeholder="请输入收款人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="收款账户" prop="payeeAccount">
          <el-input v-model="costPaymentForm.payeeAccount" placeholder="请输入收款账户" />
        </el-form-item>
        <el-form-item label="银行信息">
          <el-input v-model="costPaymentForm.bankInfo" placeholder="请输入银行信息" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="costPaymentForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="costPaymentDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitCostPayment" :loading="costPaymentSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCostSummary, addReceipt, addCostPayment } from '@/api/contract/financial'

const props = defineProps({
  contractId: {
    type: Number,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 汇总数据
const summaryData = ref({
  contractCost: 0,
  totalReceiptAmount: 0,
  totalCostPaymentAmount: 0,
  pendingCostPaymentAmount: 0
})

// 收票记录
const receiptList = ref([])
// 付款记录
const costPaymentList = ref([])

// 对话框状态
const receiptDialogVisible = ref(false)
const costPaymentDialogVisible = ref(false)
const receiptSubmitting = ref(false)
const costPaymentSubmitting = ref(false)

// 表单引用
const receiptFormRef = ref(null)
const costPaymentFormRef = ref(null)

// 收票表单
const receiptForm = ref({
  contractId: null,
  receiptNumber: '',
  receiptAmount: null,
  receiptDate: '',
  receiptType: '',
  supplierName: '',
  remark: ''
})

// 付款表单
const costPaymentForm = ref({
  contractId: null,
  paymentAmount: null,
  paymentDate: '',
  paymentMethod: '',
  payeeName: '',
  payeeAccount: '',
  bankInfo: '',
  remark: ''
})

// 表单验证规则
const receiptRules = {
  receiptNumber: [{ required: true, message: '请输入发票号码', trigger: 'blur' }],
  receiptAmount: [{ required: true, message: '请输入收票金额', trigger: 'blur' }],
  receiptDate: [{ required: true, message: '请选择收票日期', trigger: 'change' }],
  receiptType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
  supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }]
}

const costPaymentRules = {
  paymentAmount: [{ required: true, message: '请输入付款金额', trigger: 'blur' }],
  paymentDate: [{ required: true, message: '请选择付款日期', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
  payeeName: [{ required: true, message: '请输入收款人', trigger: 'blur' }]
}

// 加载数据
const loadData = async () => {
  if (!props.contractId) return
  
  try {
    const response = await getCostSummary(props.contractId)
    if (response.code === 200) {
      const data = response.data
      summaryData.value = {
        contractCost: data.contractCost || 0,
        totalReceiptAmount: data.totalReceiptAmount || 0,
        totalCostPaymentAmount: data.totalCostPaymentAmount || 0,
        pendingCostPaymentAmount: data.pendingCostPaymentAmount || 0
      }
      receiptList.value = data.receipts || []
      costPaymentList.value = data.costPayments || []
    }
  } catch (error) {
    console.error('加载成本数据失败:', error)
  }
}

// 显示新增收票对话框
const showAddReceiptDialog = () => {
  receiptForm.value = {
    contractId: props.contractId,
    receiptNumber: '',
    receiptAmount: null,
    receiptDate: '',
    receiptType: '',
    supplierName: '',
    remark: ''
  }
  receiptDialogVisible.value = true
}

// 显示新增付款对话框
const showAddCostPaymentDialog = () => {
  costPaymentForm.value = {
    contractId: props.contractId,
    paymentAmount: null,
    paymentDate: '',
    paymentMethod: '',
    payeeName: '',
    payeeAccount: '',
    bankInfo: '',
    remark: ''
  }
  costPaymentDialogVisible.value = true
}

// 提交收票记录
const submitReceipt = async () => {
  try {
    await receiptFormRef.value.validate()
    receiptSubmitting.value = true
    
    const response = await addReceipt(receiptForm.value)
    if (response.code === 200) {
      ElMessage.success('收票记录添加成功')
      receiptDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加收票记录失败:', error)
    ElMessage.error('添加失败')
  } finally {
    receiptSubmitting.value = false
  }
}

// 提交付款记录
const submitCostPayment = async () => {
  try {
    await costPaymentFormRef.value.validate()
    costPaymentSubmitting.value = true
    
    const response = await addCostPayment(costPaymentForm.value)
    if (response.code === 200) {
      ElMessage.success('付款记录添加成功')
      costPaymentDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加付款记录失败:', error)
    ElMessage.error('添加失败')
  } finally {
    costPaymentSubmitting.value = false
  }
}

// 删除收票记录
const deleteReceipt = async (receipt) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除发票号码为"${receipt.receiptNumber}"的收票记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 删除付款记录
const deleteCostPayment = async (payment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金额为"${payment.paymentAmount}"的付款记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 监听合同ID变化
watch(() => props.contractId, (newId) => {
  if (newId) {
    loadData()
  }
}, { immediate: true })

onMounted(() => {
  if (props.contractId) {
    loadData()
  }
})
</script>

<style scoped>
.contract-cost-management {
  padding: 20px;
}

.summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-4 {
  margin-top: 16px;
}
</style>
