-- 添加分类编码字段到技术手册类目表
-- 执行时间：2024-11-01

-- 1. 添加分类编码字段
ALTER TABLE technical_manual_category 
ADD COLUMN classification_code VARCHAR(10) COMMENT '分类编码' AFTER classification;

-- 2. 创建索引以提高查询性能
-- CREATE INDEX idx_technical_manual_category_classification_code ON technical_manual_category(classification_code);

-- 3. 更新现有数据的分类编码
-- 根据分类名称生成对应的编码
UPDATE technical_manual_category 
SET classification_code = CASE 
    WHEN classification = '公共场所' THEN 'Q'
    WHEN classification = '气' THEN 'Q'
    WHEN classification = '油气回收' THEN 'Q'
    WHEN classification = '固' THEN 'G'
    WHEN classification = '土' THEN 'G'
    WHEN classification = '水处理剂' THEN 'G'
    WHEN classification = '水泥胶砂' THEN 'G'
    WHEN classification = '污泥' THEN 'G'
    WHEN classification = '海洋' AND category != '海水' THEN 'G'
    WHEN classification = '水' THEN 'S'
    WHEN classification = '海洋' AND category = '海水' THEN 'S'
    WHEN classification = '声' THEN 'Z'
    WHEN classification = '振动' THEN 'Z'
    WHEN classification = '生态' THEN 'B'
    ELSE NULL
END
WHERE classification_code IS NULL;

-- 4. 验证数据更新
SELECT 
    classification,
    classification_code,
    COUNT(*) as count
FROM technical_manual_category 
GROUP BY classification, classification_code
ORDER BY classification;

-- 5. 显示更新结果
SELECT 
    COUNT(*) as total_records,
    COUNT(classification_code) as records_with_code,
    COUNT(*) - COUNT(classification_code) as records_without_code
FROM technical_manual_category;
