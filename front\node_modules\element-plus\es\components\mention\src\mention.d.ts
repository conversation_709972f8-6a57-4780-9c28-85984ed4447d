import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type Mention from './mention.vue';
import type { MentionOption } from './types';
import type { Options } from 'element-plus/es/components/popper';
export declare const mentionProps: {
    options: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => MentionOption[]) | (() => MentionOption[]) | ((new (...args: any[]) => MentionOption[]) | (() => MentionOption[]))[], unknown, unknown, () => never[], boolean>;
    prefix: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | string[]) | (() => string | string[]) | ((new (...args: any[]) => string | string[]) | (() => string | string[]))[], unknown, unknown, string, boolean>;
    split: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, string, boolean>;
    filterOption: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => false | ((pattern: string, option: MentionOption) => boolean)) | (() => false | ((pattern: string, option: MentionOption) => boolean)) | ((new (...args: any[]) => false | ((pattern: string, option: MentionOption) => boolean)) | (() => false | ((pattern: string, option: MentionOption) => boolean)))[], unknown, unknown, () => (pattern: string, option: MentionOption) => boolean, boolean>;
    placement: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => "top" | "bottom") | (() => "top" | "bottom") | ((new (...args: any[]) => "top" | "bottom") | (() => "top" | "bottom"))[], unknown, unknown, string, boolean>;
    showArrow: BooleanConstructor;
    offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, number, boolean>;
    whole: BooleanConstructor;
    checkIsWhole: {
        readonly type: import("vue").PropType<(pattern: string, prefix: string) => boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    modelValue: StringConstructor;
    loading: BooleanConstructor;
    popperClass: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | any)[])[])[])[])[])[])[])[])[])[])[]) | (() => string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | any)[])[])[])[])[])[])[])[])[])[])[]) | ((new (...args: any[]) => string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | any)[])[])[])[])[])[])[])[])[])[])[]) | (() => string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | (string | {
            [x: string]: boolean;
        } | any)[])[])[])[])[])[])[])[])[])[])[]))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    popperStyle: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | import("vue").CSSProperties | import("vue").StyleValue[]) | (() => import("vue").StyleValue) | ((new (...args: any[]) => string | import("vue").CSSProperties | import("vue").StyleValue[]) | (() => import("vue").StyleValue))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    popperOptions: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => Partial<Options>) | (() => Partial<Options>) | ((new (...args: any[]) => Partial<Options>) | (() => Partial<Options>))[], unknown, unknown, () => Partial<Options>, boolean>;
    props: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => MentionOptionProps) | (() => MentionOptionProps) | ((new (...args: any[]) => MentionOptionProps) | (() => MentionOptionProps))[], unknown, unknown, () => Required<MentionOptionProps>, boolean>;
    inputmode: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => "search" | "text" | "none" | "url" | "email" | "tel" | "numeric" | "decimal") | (() => "search" | "text" | "none" | "url" | "email" | "tel" | "numeric" | "decimal" | undefined) | ((new (...args: any[]) => "search" | "text" | "none" | "url" | "email" | "tel" | "numeric" | "decimal") | (() => "search" | "text" | "none" | "url" | "email" | "tel" | "numeric" | "decimal" | undefined))[], unknown, unknown, undefined, boolean>;
    name: StringConstructor;
    ariaLabel: StringConstructor;
    id: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, undefined, boolean>;
    size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "small" | "default" | "large", never>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabled: BooleanConstructor;
    modelModifiers: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("element-plus/es/components/input").InputModelModifiers) | (() => import("element-plus/es/components/input").InputModelModifiers) | ((new (...args: any[]) => import("element-plus/es/components/input").InputModelModifiers) | (() => import("element-plus/es/components/input").InputModelModifiers))[], unknown, unknown, () => {}, boolean>;
    maxlength: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    minlength: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    type: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "text", boolean>;
    resize: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "none" | "both" | "horizontal" | "vertical", unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    autosize: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => boolean | {
        minRows?: number;
        maxRows?: number;
    }) | (() => import("element-plus/es/components/input").InputAutoSize) | ((new (...args: any[]) => boolean | {
        minRows?: number;
        maxRows?: number;
    }) | (() => import("element-plus/es/components/input").InputAutoSize))[], unknown, unknown, false, boolean>;
    autocomplete: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => "" | "name" | "email" | "off" | "on" | "additional-name" | "address-level1" | "address-level2" | "address-level3" | "address-level4" | "address-line1" | "address-line2" | "address-line3" | "bday-day" | "bday-month" | "bday-year" | "cc-csc" | "cc-exp" | "cc-exp-month" | "cc-exp-year" | "cc-family-name" | "cc-given-name" | "cc-name" | "cc-number" | "cc-type" | "country" | "country-name" | "current-password" | "family-name" | "given-name" | "honorific-prefix" | "honorific-suffix" | "new-password" | "one-time-code" | "organization" | "postal-code" | "street-address" | "transaction-amount" | "transaction-currency" | "username" | "tel" | "tel-area-code" | "tel-country-code" | "tel-extension" | "tel-local" | "tel-local-prefix" | "tel-local-suffix" | "tel-national" | "home email" | "home tel" | "home tel-area-code" | "home tel-country-code" | "home tel-extension" | "home tel-local" | "home tel-local-prefix" | "home tel-local-suffix" | "home tel-national" | "mobile email" | "mobile tel" | "mobile tel-area-code" | "mobile tel-country-code" | "mobile tel-extension" | "mobile tel-local" | "mobile tel-local-prefix" | "mobile tel-local-suffix" | "mobile tel-national" | "work email" | "work tel" | "work tel-area-code" | "work tel-country-code" | "work tel-extension" | "work tel-local" | "work tel-local-prefix" | "work tel-local-suffix" | "work tel-national" | "name webauthn" | "additional-name webauthn" | "address-level1 webauthn" | "address-level2 webauthn" | "address-level3 webauthn" | "address-level4 webauthn" | "address-line1 webauthn" | "address-line2 webauthn" | "address-line3 webauthn" | "bday-day webauthn" | "bday-month webauthn" | "bday-year webauthn" | "cc-csc webauthn" | "cc-exp webauthn" | "cc-exp-month webauthn" | "cc-exp-year webauthn" | "cc-family-name webauthn" | "cc-given-name webauthn" | "cc-name webauthn" | "cc-number webauthn" | "cc-type webauthn" | "country webauthn" | "country-name webauthn" | "current-password webauthn" | "family-name webauthn" | "given-name webauthn" | "honorific-prefix webauthn" | "honorific-suffix webauthn" | "new-password webauthn" | "one-time-code webauthn" | "organization webauthn" | "postal-code webauthn" | "street-address webauthn" | "transaction-amount webauthn" | "transaction-currency webauthn" | "username webauthn" | "email webauthn" | "tel webauthn" | "tel-area-code webauthn" | "tel-country-code webauthn" | "tel-extension webauthn" | "tel-local webauthn" | "tel-local-prefix webauthn" | "tel-local-suffix webauthn" | "tel-national webauthn" | "home email webauthn" | "home tel webauthn" | "home tel-area-code webauthn" | "home tel-country-code webauthn" | "home tel-extension webauthn" | "home tel-local webauthn" | "home tel-local-prefix webauthn" | "home tel-local-suffix webauthn" | "home tel-national webauthn" | "mobile email webauthn" | "mobile tel webauthn" | "mobile tel-area-code webauthn" | "mobile tel-country-code webauthn" | "mobile tel-extension webauthn" | "mobile tel-local webauthn" | "mobile tel-local-prefix webauthn" | "mobile tel-local-suffix webauthn" | "mobile tel-national webauthn" | "work email webauthn" | "work tel webauthn" | "work tel-area-code webauthn" | "work tel-country-code webauthn" | "work tel-extension webauthn" | "work tel-local webauthn" | "work tel-local-prefix webauthn" | "work tel-local-suffix webauthn" | "work tel-national webauthn" | "billing name" | "billing name webauthn" | "billing additional-name" | "billing additional-name webauthn" | "billing address-level1" | "billing address-level1 webauthn" | "billing address-level2" | "billing address-level2 webauthn" | "billing address-level3" | "billing address-level3 webauthn" | "billing address-level4" | "billing address-level4 webauthn" | "billing address-line1" | "billing address-line1 webauthn" | "billing address-line2" | "billing address-line2 webauthn" | "billing address-line3" | "billing address-line3 webauthn" | "billing bday-day" | "billing bday-day webauthn" | "billing bday-month" | "billing bday-month webauthn" | "billing bday-year" | "billing bday-year webauthn" | "billing cc-csc" | "billing cc-csc webauthn" | "billing cc-exp" | "billing cc-exp webauthn" | "billing cc-exp-month" | "billing cc-exp-month webauthn" | "billing cc-exp-year" | "billing cc-exp-year webauthn" | "billing cc-family-name" | "billing cc-family-name webauthn" | "billing cc-given-name" | "billing cc-given-name webauthn" | "billing cc-name" | "billing cc-name webauthn" | "billing cc-number" | "billing cc-number webauthn" | "billing cc-type" | "billing cc-type webauthn" | "billing country" | "billing country webauthn" | "billing country-name" | "billing country-name webauthn" | "billing current-password" | "billing current-password webauthn" | "billing family-name" | "billing family-name webauthn" | "billing given-name" | "billing given-name webauthn" | "billing honorific-prefix" | "billing honorific-prefix webauthn" | "billing honorific-suffix" | "billing honorific-suffix webauthn" | "billing new-password" | "billing new-password webauthn" | "billing one-time-code" | "billing one-time-code webauthn" | "billing organization" | "billing organization webauthn" | "billing postal-code" | "billing postal-code webauthn" | "billing street-address" | "billing street-address webauthn" | "billing transaction-amount" | "billing transaction-amount webauthn" | "billing transaction-currency" | "billing transaction-currency webauthn" | "billing username" | "billing username webauthn" | "billing email" | "billing email webauthn" | "billing tel" | "billing tel webauthn" | "billing tel-area-code" | "billing tel-area-code webauthn" | "billing tel-country-code" | "billing tel-country-code webauthn" | "billing tel-extension" | "billing tel-extension webauthn" | "billing tel-local" | "billing tel-local webauthn" | "billing tel-local-prefix" | "billing tel-local-prefix webauthn" | "billing tel-local-suffix" | "billing tel-local-suffix webauthn" | "billing tel-national" | "billing tel-national webauthn" | "billing home email" | "billing home email webauthn" | "billing home tel" | "billing home tel webauthn" | "billing home tel-area-code" | "billing home tel-area-code webauthn" | "billing home tel-country-code" | "billing home tel-country-code webauthn" | "billing home tel-extension" | "billing home tel-extension webauthn" | "billing home tel-local" | "billing home tel-local webauthn" | "billing home tel-local-prefix" | "billing home tel-local-prefix webauthn" | "billing home tel-local-suffix" | "billing home tel-local-suffix webauthn" | "billing home tel-national" | "billing home tel-national webauthn" | "billing mobile email" | "billing mobile email webauthn" | "billing mobile tel" | "billing mobile tel webauthn" | "billing mobile tel-area-code" | "billing mobile tel-area-code webauthn" | "billing mobile tel-country-code" | "billing mobile tel-country-code webauthn" | "billing mobile tel-extension" | "billing mobile tel-extension webauthn" | "billing mobile tel-local" | "billing mobile tel-local webauthn" | "billing mobile tel-local-prefix" | "billing mobile tel-local-prefix webauthn" | "billing mobile tel-local-suffix" | "billing mobile tel-local-suffix webauthn" | "billing mobile tel-national" | "billing mobile tel-national webauthn" | "billing work email" | "billing work email webauthn" | "billing work tel" | "billing work tel webauthn" | "billing work tel-area-code" | "billing work tel-area-code webauthn" | "billing work tel-country-code" | "billing work tel-country-code webauthn" | "billing work tel-extension" | "billing work tel-extension webauthn" | "billing work tel-local" | "billing work tel-local webauthn" | "billing work tel-local-prefix" | "billing work tel-local-prefix webauthn" | "billing work tel-local-suffix" | "billing work tel-local-suffix webauthn" | "billing work tel-national" | "billing work tel-national webauthn" | "shipping name" | "shipping name webauthn" | "shipping additional-name" | "shipping additional-name webauthn" | "shipping address-level1" | "shipping address-level1 webauthn" | "shipping address-level2" | "shipping address-level2 webauthn" | "shipping address-level3" | "shipping address-level3 webauthn" | "shipping address-level4" | "shipping address-level4 webauthn" | "shipping address-line1" | "shipping address-line1 webauthn" | "shipping address-line2" | "shipping address-line2 webauthn" | "shipping address-line3" | "shipping address-line3 webauthn" | "shipping bday-day" | "shipping bday-day webauthn" | "shipping bday-month" | "shipping bday-month webauthn" | "shipping bday-year" | "shipping bday-year webauthn" | "shipping cc-csc" | "shipping cc-csc webauthn" | "shipping cc-exp" | "shipping cc-exp webauthn" | "shipping cc-exp-month" | "shipping cc-exp-month webauthn" | "shipping cc-exp-year" | "shipping cc-exp-year webauthn" | "shipping cc-family-name" | "shipping cc-family-name webauthn" | "shipping cc-given-name" | "shipping cc-given-name webauthn" | "shipping cc-name" | "shipping cc-name webauthn" | "shipping cc-number" | "shipping cc-number webauthn" | "shipping cc-type" | "shipping cc-type webauthn" | "shipping country" | "shipping country webauthn" | "shipping country-name" | "shipping country-name webauthn" | "shipping current-password" | "shipping current-password webauthn" | "shipping family-name" | "shipping family-name webauthn" | "shipping given-name" | "shipping given-name webauthn" | "shipping honorific-prefix" | "shipping honorific-prefix webauthn" | "shipping honorific-suffix" | "shipping honorific-suffix webauthn" | "shipping new-password" | "shipping new-password webauthn" | "shipping one-time-code" | "shipping one-time-code webauthn" | "shipping organization" | "shipping organization webauthn" | "shipping postal-code" | "shipping postal-code webauthn" | "shipping street-address" | "shipping street-address webauthn" | "shipping transaction-amount" | "shipping transaction-amount webauthn" | "shipping transaction-currency" | "shipping transaction-currency webauthn" | "shipping username" | "shipping username webauthn" | "shipping email" | "shipping email webauthn" | "shipping tel" | "shipping tel webauthn" | "shipping tel-area-code" | "shipping tel-area-code webauthn" | "shipping tel-country-code" | "shipping tel-country-code webauthn" | "shipping tel-extension" | "shipping tel-extension webauthn" | "shipping tel-local" | "shipping tel-local webauthn" | "shipping tel-local-prefix" | "shipping tel-local-prefix webauthn" | "shipping tel-local-suffix" | "shipping tel-local-suffix webauthn" | "shipping tel-national" | "shipping tel-national webauthn" | "shipping home email" | "shipping home email webauthn" | "shipping home tel" | "shipping home tel webauthn" | "shipping home tel-area-code" | "shipping home tel-area-code webauthn" | "shipping home tel-country-code" | "shipping home tel-country-code webauthn" | "shipping home tel-extension" | "shipping home tel-extension webauthn" | "shipping home tel-local" | "shipping home tel-local webauthn" | "shipping home tel-local-prefix" | "shipping home tel-local-prefix webauthn" | "shipping home tel-local-suffix" | "shipping home tel-local-suffix webauthn" | "shipping home tel-national" | "shipping home tel-national webauthn" | "shipping mobile email" | "shipping mobile email webauthn" | "shipping mobile tel" | "shipping mobile tel webauthn" | "shipping mobile tel-area-code" | "shipping mobile tel-area-code webauthn" | "shipping mobile tel-country-code" | "shipping mobile tel-country-code webauthn" | "shipping mobile tel-extension" | "shipping mobile tel-extension webauthn" | "shipping mobile tel-local" | "shipping mobile tel-local webauthn" | "shipping mobile tel-local-prefix" | "shipping mobile tel-local-prefix webauthn" | "shipping mobile tel-local-suffix" | "shipping mobile tel-local-suffix webauthn" | "shipping mobile tel-national" | "shipping mobile tel-national webauthn" | "shipping work email" | "shipping work email webauthn" | "shipping work tel" | "shipping work tel webauthn" | "shipping work tel-area-code" | "shipping work tel-area-code webauthn" | "shipping work tel-country-code" | "shipping work tel-country-code webauthn" | "shipping work tel-extension" | "shipping work tel-extension webauthn" | "shipping work tel-local" | "shipping work tel-local webauthn" | "shipping work tel-local-prefix" | "shipping work tel-local-prefix webauthn" | "shipping work tel-local-suffix" | "shipping work tel-local-suffix webauthn" | "shipping work tel-national" | "shipping work tel-national webauthn" | `section-${string} name` | `section-${string} name webauthn` | `section-${string} additional-name` | `section-${string} additional-name webauthn` | `section-${string} address-level1` | `section-${string} address-level1 webauthn` | `section-${string} address-level2` | `section-${string} address-level2 webauthn` | `section-${string} address-level3` | `section-${string} address-level3 webauthn` | `section-${string} address-level4` | `section-${string} address-level4 webauthn` | `section-${string} address-line1` | `section-${string} address-line1 webauthn` | `section-${string} address-line2` | `section-${string} address-line2 webauthn` | `section-${string} address-line3` | `section-${string} address-line3 webauthn` | `section-${string} bday-day` | `section-${string} bday-day webauthn` | `section-${string} bday-month` | `section-${string} bday-month webauthn` | `section-${string} bday-year` | `section-${string} bday-year webauthn` | `section-${string} cc-csc` | `section-${string} cc-csc webauthn` | `section-${string} cc-exp` | `section-${string} cc-exp webauthn` | `section-${string} cc-exp-month` | `section-${string} cc-exp-month webauthn` | `section-${string} cc-exp-year` | `section-${string} cc-exp-year webauthn` | `section-${string} cc-family-name` | `section-${string} cc-family-name webauthn` | `section-${string} cc-given-name` | `section-${string} cc-given-name webauthn` | `section-${string} cc-name` | `section-${string} cc-name webauthn` | `section-${string} cc-number` | `section-${string} cc-number webauthn` | `section-${string} cc-type` | `section-${string} cc-type webauthn` | `section-${string} country` | `section-${string} country webauthn` | `section-${string} country-name` | `section-${string} country-name webauthn` | `section-${string} current-password` | `section-${string} current-password webauthn` | `section-${string} family-name` | `section-${string} family-name webauthn` | `section-${string} given-name` | `section-${string} given-name webauthn` | `section-${string} honorific-prefix` | `section-${string} honorific-prefix webauthn` | `section-${string} honorific-suffix` | `section-${string} honorific-suffix webauthn` | `section-${string} new-password` | `section-${string} new-password webauthn` | `section-${string} one-time-code` | `section-${string} one-time-code webauthn` | `section-${string} organization` | `section-${string} organization webauthn` | `section-${string} postal-code` | `section-${string} postal-code webauthn` | `section-${string} street-address` | `section-${string} street-address webauthn` | `section-${string} transaction-amount` | `section-${string} transaction-amount webauthn` | `section-${string} transaction-currency` | `section-${string} transaction-currency webauthn` | `section-${string} username` | `section-${string} username webauthn` | `section-${string} email` | `section-${string} email webauthn` | `section-${string} tel` | `section-${string} tel webauthn` | `section-${string} tel-area-code` | `section-${string} tel-area-code webauthn` | `section-${string} tel-country-code` | `section-${string} tel-country-code webauthn` | `section-${string} tel-extension` | `section-${string} tel-extension webauthn` | `section-${string} tel-local` | `section-${string} tel-local webauthn` | `section-${string} tel-local-prefix` | `section-${string} tel-local-prefix webauthn` | `section-${string} tel-local-suffix` | `section-${string} tel-local-suffix webauthn` | `section-${string} tel-national` | `section-${string} tel-national webauthn` | `section-${string} home email` | `section-${string} home email webauthn` | `section-${string} home tel` | `section-${string} home tel webauthn` | `section-${string} home tel-area-code` | `section-${string} home tel-area-code webauthn` | `section-${string} home tel-country-code` | `section-${string} home tel-country-code webauthn` | `section-${string} home tel-extension` | `section-${string} home tel-extension webauthn` | `section-${string} home tel-local` | `section-${string} home tel-local webauthn` | `section-${string} home tel-local-prefix` | `section-${string} home tel-local-prefix webauthn` | `section-${string} home tel-local-suffix` | `section-${string} home tel-local-suffix webauthn` | `section-${string} home tel-national` | `section-${string} home tel-national webauthn` | `section-${string} mobile email` | `section-${string} mobile email webauthn` | `section-${string} mobile tel` | `section-${string} mobile tel webauthn` | `section-${string} mobile tel-area-code` | `section-${string} mobile tel-area-code webauthn` | `section-${string} mobile tel-country-code` | `section-${string} mobile tel-country-code webauthn` | `section-${string} mobile tel-extension` | `section-${string} mobile tel-extension webauthn` | `section-${string} mobile tel-local` | `section-${string} mobile tel-local webauthn` | `section-${string} mobile tel-local-prefix` | `section-${string} mobile tel-local-prefix webauthn` | `section-${string} mobile tel-local-suffix` | `section-${string} mobile tel-local-suffix webauthn` | `section-${string} mobile tel-national` | `section-${string} mobile tel-national webauthn` | `section-${string} work email` | `section-${string} work email webauthn` | `section-${string} work tel` | `section-${string} work tel webauthn` | `section-${string} work tel-area-code` | `section-${string} work tel-area-code webauthn` | `section-${string} work tel-country-code` | `section-${string} work tel-country-code webauthn` | `section-${string} work tel-extension` | `section-${string} work tel-extension webauthn` | `section-${string} work tel-local` | `section-${string} work tel-local webauthn` | `section-${string} work tel-local-prefix` | `section-${string} work tel-local-prefix webauthn` | `section-${string} work tel-local-suffix` | `section-${string} work tel-local-suffix webauthn` | `section-${string} work tel-national` | `section-${string} work tel-national webauthn` | `section-${string} billing name` | `section-${string} billing name webauthn` | `section-${string} billing additional-name` | `section-${string} billing additional-name webauthn` | `section-${string} billing address-level1` | `section-${string} billing address-level1 webauthn` | `section-${string} billing address-level2` | `section-${string} billing address-level2 webauthn` | `section-${string} billing address-level3` | `section-${string} billing address-level3 webauthn` | `section-${string} billing address-level4` | `section-${string} billing address-level4 webauthn` | `section-${string} billing address-line1` | `section-${string} billing address-line1 webauthn` | `section-${string} billing address-line2` | `section-${string} billing address-line2 webauthn` | `section-${string} billing address-line3` | `section-${string} billing address-line3 webauthn` | `section-${string} billing bday-day` | `section-${string} billing bday-day webauthn` | `section-${string} billing bday-month` | `section-${string} billing bday-month webauthn` | `section-${string} billing bday-year` | `section-${string} billing bday-year webauthn` | `section-${string} billing cc-csc` | `section-${string} billing cc-csc webauthn` | `section-${string} billing cc-exp` | `section-${string} billing cc-exp webauthn` | `section-${string} billing cc-exp-month` | `section-${string} billing cc-exp-month webauthn` | `section-${string} billing cc-exp-year` | `section-${string} billing cc-exp-year webauthn` | `section-${string} billing cc-family-name` | `section-${string} billing cc-family-name webauthn` | `section-${string} billing cc-given-name` | `section-${string} billing cc-given-name webauthn` | `section-${string} billing cc-name` | `section-${string} billing cc-name webauthn` | `section-${string} billing cc-number` | `section-${string} billing cc-number webauthn` | `section-${string} billing cc-type` | `section-${string} billing cc-type webauthn` | `section-${string} billing country` | `section-${string} billing country webauthn` | `section-${string} billing country-name` | `section-${string} billing country-name webauthn` | `section-${string} billing current-password` | `section-${string} billing current-password webauthn` | `section-${string} billing family-name` | `section-${string} billing family-name webauthn` | `section-${string} billing given-name` | `section-${string} billing given-name webauthn` | `section-${string} billing honorific-prefix` | `section-${string} billing honorific-prefix webauthn` | `section-${string} billing honorific-suffix` | `section-${string} billing honorific-suffix webauthn` | `section-${string} billing new-password` | `section-${string} billing new-password webauthn` | `section-${string} billing one-time-code` | `section-${string} billing one-time-code webauthn` | `section-${string} billing organization` | `section-${string} billing organization webauthn` | `section-${string} billing postal-code` | `section-${string} billing postal-code webauthn` | `section-${string} billing street-address` | `section-${string} billing street-address webauthn` | `section-${string} billing transaction-amount` | `section-${string} billing transaction-amount webauthn` | `section-${string} billing transaction-currency` | `section-${string} billing transaction-currency webauthn` | `section-${string} billing username` | `section-${string} billing username webauthn` | `section-${string} billing email` | `section-${string} billing email webauthn` | `section-${string} billing tel` | `section-${string} billing tel webauthn` | `section-${string} billing tel-area-code` | `section-${string} billing tel-area-code webauthn` | `section-${string} billing tel-country-code` | `section-${string} billing tel-country-code webauthn` | `section-${string} billing tel-extension` | `section-${string} billing tel-extension webauthn` | `section-${string} billing tel-local` | `section-${string} billing tel-local webauthn` | `section-${string} billing tel-local-prefix` | `section-${string} billing tel-local-prefix webauthn` | `section-${string} billing tel-local-suffix` | `section-${string} billing tel-local-suffix webauthn` | `section-${string} billing tel-national` | `section-${string} billing tel-national webauthn` | `section-${string} billing home email` | `section-${string} billing home email webauthn` | `section-${string} billing home tel` | `section-${string} billing home tel webauthn` | `section-${string} billing home tel-area-code` | `section-${string} billing home tel-area-code webauthn` | `section-${string} billing home tel-country-code` | `section-${string} billing home tel-country-code webauthn` | `section-${string} billing home tel-extension` | `section-${string} billing home tel-extension webauthn` | `section-${string} billing home tel-local` | `section-${string} billing home tel-local webauthn` | `section-${string} billing home tel-local-prefix` | `section-${string} billing home tel-local-prefix webauthn` | `section-${string} billing home tel-local-suffix` | `section-${string} billing home tel-local-suffix webauthn` | `section-${string} billing home tel-national` | `section-${string} billing home tel-national webauthn` | `section-${string} billing mobile email` | `section-${string} billing mobile email webauthn` | `section-${string} billing mobile tel` | `section-${string} billing mobile tel webauthn` | `section-${string} billing mobile tel-area-code` | `section-${string} billing mobile tel-area-code webauthn` | `section-${string} billing mobile tel-country-code` | `section-${string} billing mobile tel-country-code webauthn` | `section-${string} billing mobile tel-extension` | `section-${string} billing mobile tel-extension webauthn` | `section-${string} billing mobile tel-local` | `section-${string} billing mobile tel-local webauthn` | `section-${string} billing mobile tel-local-prefix` | `section-${string} billing mobile tel-local-prefix webauthn` | `section-${string} billing mobile tel-local-suffix` | `section-${string} billing mobile tel-local-suffix webauthn` | `section-${string} billing mobile tel-national` | `section-${string} billing mobile tel-national webauthn` | `section-${string} billing work email` | `section-${string} billing work email webauthn` | `section-${string} billing work tel` | `section-${string} billing work tel webauthn` | `section-${string} billing work tel-area-code` | `section-${string} billing work tel-area-code webauthn` | `section-${string} billing work tel-country-code` | `section-${string} billing work tel-country-code webauthn` | `section-${string} billing work tel-extension` | `section-${string} billing work tel-extension webauthn` | `section-${string} billing work tel-local` | `section-${string} billing work tel-local webauthn` | `section-${string} billing work tel-local-prefix` | `section-${string} billing work tel-local-prefix webauthn` | `section-${string} billing work tel-local-suffix` | `section-${string} billing work tel-local-suffix webauthn` | `section-${string} billing work tel-national` | `section-${string} billing work tel-national webauthn` | `section-${string} shipping name` | `section-${string} shipping name webauthn` | `section-${string} shipping additional-name` | `section-${string} shipping additional-name webauthn` | `section-${string} shipping address-level1` | `section-${string} shipping address-level1 webauthn` | `section-${string} shipping address-level2` | `section-${string} shipping address-level2 webauthn` | `section-${string} shipping address-level3` | `section-${string} shipping address-level3 webauthn` | `section-${string} shipping address-level4` | `section-${string} shipping address-level4 webauthn` | `section-${string} shipping address-line1` | `section-${string} shipping address-line1 webauthn` | `section-${string} shipping address-line2` | `section-${string} shipping address-line2 webauthn` | `section-${string} shipping address-line3` | `section-${string} shipping address-line3 webauthn` | `section-${string} shipping bday-day` | `section-${string} shipping bday-day webauthn` | `section-${string} shipping bday-month` | `section-${string} shipping bday-month webauthn` | `section-${string} shipping bday-year` | `section-${string} shipping bday-year webauthn` | `section-${string} shipping cc-csc` | `section-${string} shipping cc-csc webauthn` | `section-${string} shipping cc-exp` | `section-${string} shipping cc-exp webauthn` | `section-${string} shipping cc-exp-month` | `section-${string} shipping cc-exp-month webauthn` | `section-${string} shipping cc-exp-year` | `section-${string} shipping cc-exp-year webauthn` | `section-${string} shipping cc-family-name` | `section-${string} shipping cc-family-name webauthn` | `section-${string} shipping cc-given-name` | `section-${string} shipping cc-given-name webauthn` | `section-${string} shipping cc-name` | `section-${string} shipping cc-name webauthn` | `section-${string} shipping cc-number` | `section-${string} shipping cc-number webauthn` | `section-${string} shipping cc-type` | `section-${string} shipping cc-type webauthn` | `section-${string} shipping country` | `section-${string} shipping country webauthn` | `section-${string} shipping country-name` | `section-${string} shipping country-name webauthn` | `section-${string} shipping current-password` | `section-${string} shipping current-password webauthn` | `section-${string} shipping family-name` | `section-${string} shipping family-name webauthn` | `section-${string} shipping given-name` | `section-${string} shipping given-name webauthn` | `section-${string} shipping honorific-prefix` | `section-${string} shipping honorific-prefix webauthn` | `section-${string} shipping honorific-suffix` | `section-${string} shipping honorific-suffix webauthn` | `section-${string} shipping new-password` | `section-${string} shipping new-password webauthn` | `section-${string} shipping one-time-code` | `section-${string} shipping one-time-code webauthn` | `section-${string} shipping organization` | `section-${string} shipping organization webauthn` | `section-${string} shipping postal-code` | `section-${string} shipping postal-code webauthn` | `section-${string} shipping street-address` | `section-${string} shipping street-address webauthn` | `section-${string} shipping transaction-amount` | `section-${string} shipping transaction-amount webauthn` | `section-${string} shipping transaction-currency` | `section-${string} shipping transaction-currency webauthn` | `section-${string} shipping username` | `section-${string} shipping username webauthn` | `section-${string} shipping email` | `section-${string} shipping email webauthn` | `section-${string} shipping tel` | `section-${string} shipping tel webauthn` | `section-${string} shipping tel-area-code` | `section-${string} shipping tel-area-code webauthn` | `section-${string} shipping tel-country-code` | `section-${string} shipping tel-country-code webauthn` | `section-${string} shipping tel-extension` | `section-${string} shipping tel-extension webauthn` | `section-${string} shipping tel-local` | `section-${string} shipping tel-local webauthn` | `section-${string} shipping tel-local-prefix` | `section-${string} shipping tel-local-prefix webauthn` | `section-${string} shipping tel-local-suffix` | `section-${string} shipping tel-local-suffix webauthn` | `section-${string} shipping tel-national` | `section-${string} shipping tel-national webauthn` | `section-${string} shipping home email` | `section-${string} shipping home email webauthn` | `section-${string} shipping home tel` | `section-${string} shipping home tel webauthn` | `section-${string} shipping home tel-area-code` | `section-${string} shipping home tel-area-code webauthn` | `section-${string} shipping home tel-country-code` | `section-${string} shipping home tel-country-code webauthn` | `section-${string} shipping home tel-extension` | `section-${string} shipping home tel-extension webauthn` | `section-${string} shipping home tel-local` | `section-${string} shipping home tel-local webauthn` | `section-${string} shipping home tel-local-prefix` | `section-${string} shipping home tel-local-prefix webauthn` | `section-${string} shipping home tel-local-suffix` | `section-${string} shipping home tel-local-suffix webauthn` | `section-${string} shipping home tel-national` | `section-${string} shipping home tel-national webauthn` | `section-${string} shipping mobile email` | `section-${string} shipping mobile email webauthn` | `section-${string} shipping mobile tel` | `section-${string} shipping mobile tel webauthn` | `section-${string} shipping mobile tel-area-code` | `section-${string} shipping mobile tel-area-code webauthn` | `section-${string} shipping mobile tel-country-code` | `section-${string} shipping mobile tel-country-code webauthn` | `section-${string} shipping mobile tel-extension` | `section-${string} shipping mobile tel-extension webauthn` | `section-${string} shipping mobile tel-local` | `section-${string} shipping mobile tel-local webauthn` | `section-${string} shipping mobile tel-local-prefix` | `section-${string} shipping mobile tel-local-prefix webauthn` | `section-${string} shipping mobile tel-local-suffix` | `section-${string} shipping mobile tel-local-suffix webauthn` | `section-${string} shipping mobile tel-national` | `section-${string} shipping mobile tel-national webauthn` | `section-${string} shipping work email` | `section-${string} shipping work email webauthn` | `section-${string} shipping work tel` | `section-${string} shipping work tel webauthn` | `section-${string} shipping work tel-area-code` | `section-${string} shipping work tel-area-code webauthn` | `section-${string} shipping work tel-country-code` | `section-${string} shipping work tel-country-code webauthn` | `section-${string} shipping work tel-extension` | `section-${string} shipping work tel-extension webauthn` | `section-${string} shipping work tel-local` | `section-${string} shipping work tel-local webauthn` | `section-${string} shipping work tel-local-prefix` | `section-${string} shipping work tel-local-prefix webauthn` | `section-${string} shipping work tel-local-suffix` | `section-${string} shipping work tel-local-suffix webauthn` | `section-${string} shipping work tel-national` | `section-${string} shipping work tel-national webauthn`) | (() => AutoFill) | ((new (...args: any[]) => "" | "name" | "email" | "off" | "on" | "additional-name" | "address-level1" | "address-level2" | "address-level3" | "address-level4" | "address-line1" | "address-line2" | "address-line3" | "bday-day" | "bday-month" | "bday-year" | "cc-csc" | "cc-exp" | "cc-exp-month" | "cc-exp-year" | "cc-family-name" | "cc-given-name" | "cc-name" | "cc-number" | "cc-type" | "country" | "country-name" | "current-password" | "family-name" | "given-name" | "honorific-prefix" | "honorific-suffix" | "new-password" | "one-time-code" | "organization" | "postal-code" | "street-address" | "transaction-amount" | "transaction-currency" | "username" | "tel" | "tel-area-code" | "tel-country-code" | "tel-extension" | "tel-local" | "tel-local-prefix" | "tel-local-suffix" | "tel-national" | "home email" | "home tel" | "home tel-area-code" | "home tel-country-code" | "home tel-extension" | "home tel-local" | "home tel-local-prefix" | "home tel-local-suffix" | "home tel-national" | "mobile email" | "mobile tel" | "mobile tel-area-code" | "mobile tel-country-code" | "mobile tel-extension" | "mobile tel-local" | "mobile tel-local-prefix" | "mobile tel-local-suffix" | "mobile tel-national" | "work email" | "work tel" | "work tel-area-code" | "work tel-country-code" | "work tel-extension" | "work tel-local" | "work tel-local-prefix" | "work tel-local-suffix" | "work tel-national" | "name webauthn" | "additional-name webauthn" | "address-level1 webauthn" | "address-level2 webauthn" | "address-level3 webauthn" | "address-level4 webauthn" | "address-line1 webauthn" | "address-line2 webauthn" | "address-line3 webauthn" | "bday-day webauthn" | "bday-month webauthn" | "bday-year webauthn" | "cc-csc webauthn" | "cc-exp webauthn" | "cc-exp-month webauthn" | "cc-exp-year webauthn" | "cc-family-name webauthn" | "cc-given-name webauthn" | "cc-name webauthn" | "cc-number webauthn" | "cc-type webauthn" | "country webauthn" | "country-name webauthn" | "current-password webauthn" | "family-name webauthn" | "given-name webauthn" | "honorific-prefix webauthn" | "honorific-suffix webauthn" | "new-password webauthn" | "one-time-code webauthn" | "organization webauthn" | "postal-code webauthn" | "street-address webauthn" | "transaction-amount webauthn" | "transaction-currency webauthn" | "username webauthn" | "email webauthn" | "tel webauthn" | "tel-area-code webauthn" | "tel-country-code webauthn" | "tel-extension webauthn" | "tel-local webauthn" | "tel-local-prefix webauthn" | "tel-local-suffix webauthn" | "tel-national webauthn" | "home email webauthn" | "home tel webauthn" | "home tel-area-code webauthn" | "home tel-country-code webauthn" | "home tel-extension webauthn" | "home tel-local webauthn" | "home tel-local-prefix webauthn" | "home tel-local-suffix webauthn" | "home tel-national webauthn" | "mobile email webauthn" | "mobile tel webauthn" | "mobile tel-area-code webauthn" | "mobile tel-country-code webauthn" | "mobile tel-extension webauthn" | "mobile tel-local webauthn" | "mobile tel-local-prefix webauthn" | "mobile tel-local-suffix webauthn" | "mobile tel-national webauthn" | "work email webauthn" | "work tel webauthn" | "work tel-area-code webauthn" | "work tel-country-code webauthn" | "work tel-extension webauthn" | "work tel-local webauthn" | "work tel-local-prefix webauthn" | "work tel-local-suffix webauthn" | "work tel-national webauthn" | "billing name" | "billing name webauthn" | "billing additional-name" | "billing additional-name webauthn" | "billing address-level1" | "billing address-level1 webauthn" | "billing address-level2" | "billing address-level2 webauthn" | "billing address-level3" | "billing address-level3 webauthn" | "billing address-level4" | "billing address-level4 webauthn" | "billing address-line1" | "billing address-line1 webauthn" | "billing address-line2" | "billing address-line2 webauthn" | "billing address-line3" | "billing address-line3 webauthn" | "billing bday-day" | "billing bday-day webauthn" | "billing bday-month" | "billing bday-month webauthn" | "billing bday-year" | "billing bday-year webauthn" | "billing cc-csc" | "billing cc-csc webauthn" | "billing cc-exp" | "billing cc-exp webauthn" | "billing cc-exp-month" | "billing cc-exp-month webauthn" | "billing cc-exp-year" | "billing cc-exp-year webauthn" | "billing cc-family-name" | "billing cc-family-name webauthn" | "billing cc-given-name" | "billing cc-given-name webauthn" | "billing cc-name" | "billing cc-name webauthn" | "billing cc-number" | "billing cc-number webauthn" | "billing cc-type" | "billing cc-type webauthn" | "billing country" | "billing country webauthn" | "billing country-name" | "billing country-name webauthn" | "billing current-password" | "billing current-password webauthn" | "billing family-name" | "billing family-name webauthn" | "billing given-name" | "billing given-name webauthn" | "billing honorific-prefix" | "billing honorific-prefix webauthn" | "billing honorific-suffix" | "billing honorific-suffix webauthn" | "billing new-password" | "billing new-password webauthn" | "billing one-time-code" | "billing one-time-code webauthn" | "billing organization" | "billing organization webauthn" | "billing postal-code" | "billing postal-code webauthn" | "billing street-address" | "billing street-address webauthn" | "billing transaction-amount" | "billing transaction-amount webauthn" | "billing transaction-currency" | "billing transaction-currency webauthn" | "billing username" | "billing username webauthn" | "billing email" | "billing email webauthn" | "billing tel" | "billing tel webauthn" | "billing tel-area-code" | "billing tel-area-code webauthn" | "billing tel-country-code" | "billing tel-country-code webauthn" | "billing tel-extension" | "billing tel-extension webauthn" | "billing tel-local" | "billing tel-local webauthn" | "billing tel-local-prefix" | "billing tel-local-prefix webauthn" | "billing tel-local-suffix" | "billing tel-local-suffix webauthn" | "billing tel-national" | "billing tel-national webauthn" | "billing home email" | "billing home email webauthn" | "billing home tel" | "billing home tel webauthn" | "billing home tel-area-code" | "billing home tel-area-code webauthn" | "billing home tel-country-code" | "billing home tel-country-code webauthn" | "billing home tel-extension" | "billing home tel-extension webauthn" | "billing home tel-local" | "billing home tel-local webauthn" | "billing home tel-local-prefix" | "billing home tel-local-prefix webauthn" | "billing home tel-local-suffix" | "billing home tel-local-suffix webauthn" | "billing home tel-national" | "billing home tel-national webauthn" | "billing mobile email" | "billing mobile email webauthn" | "billing mobile tel" | "billing mobile tel webauthn" | "billing mobile tel-area-code" | "billing mobile tel-area-code webauthn" | "billing mobile tel-country-code" | "billing mobile tel-country-code webauthn" | "billing mobile tel-extension" | "billing mobile tel-extension webauthn" | "billing mobile tel-local" | "billing mobile tel-local webauthn" | "billing mobile tel-local-prefix" | "billing mobile tel-local-prefix webauthn" | "billing mobile tel-local-suffix" | "billing mobile tel-local-suffix webauthn" | "billing mobile tel-national" | "billing mobile tel-national webauthn" | "billing work email" | "billing work email webauthn" | "billing work tel" | "billing work tel webauthn" | "billing work tel-area-code" | "billing work tel-area-code webauthn" | "billing work tel-country-code" | "billing work tel-country-code webauthn" | "billing work tel-extension" | "billing work tel-extension webauthn" | "billing work tel-local" | "billing work tel-local webauthn" | "billing work tel-local-prefix" | "billing work tel-local-prefix webauthn" | "billing work tel-local-suffix" | "billing work tel-local-suffix webauthn" | "billing work tel-national" | "billing work tel-national webauthn" | "shipping name" | "shipping name webauthn" | "shipping additional-name" | "shipping additional-name webauthn" | "shipping address-level1" | "shipping address-level1 webauthn" | "shipping address-level2" | "shipping address-level2 webauthn" | "shipping address-level3" | "shipping address-level3 webauthn" | "shipping address-level4" | "shipping address-level4 webauthn" | "shipping address-line1" | "shipping address-line1 webauthn" | "shipping address-line2" | "shipping address-line2 webauthn" | "shipping address-line3" | "shipping address-line3 webauthn" | "shipping bday-day" | "shipping bday-day webauthn" | "shipping bday-month" | "shipping bday-month webauthn" | "shipping bday-year" | "shipping bday-year webauthn" | "shipping cc-csc" | "shipping cc-csc webauthn" | "shipping cc-exp" | "shipping cc-exp webauthn" | "shipping cc-exp-month" | "shipping cc-exp-month webauthn" | "shipping cc-exp-year" | "shipping cc-exp-year webauthn" | "shipping cc-family-name" | "shipping cc-family-name webauthn" | "shipping cc-given-name" | "shipping cc-given-name webauthn" | "shipping cc-name" | "shipping cc-name webauthn" | "shipping cc-number" | "shipping cc-number webauthn" | "shipping cc-type" | "shipping cc-type webauthn" | "shipping country" | "shipping country webauthn" | "shipping country-name" | "shipping country-name webauthn" | "shipping current-password" | "shipping current-password webauthn" | "shipping family-name" | "shipping family-name webauthn" | "shipping given-name" | "shipping given-name webauthn" | "shipping honorific-prefix" | "shipping honorific-prefix webauthn" | "shipping honorific-suffix" | "shipping honorific-suffix webauthn" | "shipping new-password" | "shipping new-password webauthn" | "shipping one-time-code" | "shipping one-time-code webauthn" | "shipping organization" | "shipping organization webauthn" | "shipping postal-code" | "shipping postal-code webauthn" | "shipping street-address" | "shipping street-address webauthn" | "shipping transaction-amount" | "shipping transaction-amount webauthn" | "shipping transaction-currency" | "shipping transaction-currency webauthn" | "shipping username" | "shipping username webauthn" | "shipping email" | "shipping email webauthn" | "shipping tel" | "shipping tel webauthn" | "shipping tel-area-code" | "shipping tel-area-code webauthn" | "shipping tel-country-code" | "shipping tel-country-code webauthn" | "shipping tel-extension" | "shipping tel-extension webauthn" | "shipping tel-local" | "shipping tel-local webauthn" | "shipping tel-local-prefix" | "shipping tel-local-prefix webauthn" | "shipping tel-local-suffix" | "shipping tel-local-suffix webauthn" | "shipping tel-national" | "shipping tel-national webauthn" | "shipping home email" | "shipping home email webauthn" | "shipping home tel" | "shipping home tel webauthn" | "shipping home tel-area-code" | "shipping home tel-area-code webauthn" | "shipping home tel-country-code" | "shipping home tel-country-code webauthn" | "shipping home tel-extension" | "shipping home tel-extension webauthn" | "shipping home tel-local" | "shipping home tel-local webauthn" | "shipping home tel-local-prefix" | "shipping home tel-local-prefix webauthn" | "shipping home tel-local-suffix" | "shipping home tel-local-suffix webauthn" | "shipping home tel-national" | "shipping home tel-national webauthn" | "shipping mobile email" | "shipping mobile email webauthn" | "shipping mobile tel" | "shipping mobile tel webauthn" | "shipping mobile tel-area-code" | "shipping mobile tel-area-code webauthn" | "shipping mobile tel-country-code" | "shipping mobile tel-country-code webauthn" | "shipping mobile tel-extension" | "shipping mobile tel-extension webauthn" | "shipping mobile tel-local" | "shipping mobile tel-local webauthn" | "shipping mobile tel-local-prefix" | "shipping mobile tel-local-prefix webauthn" | "shipping mobile tel-local-suffix" | "shipping mobile tel-local-suffix webauthn" | "shipping mobile tel-national" | "shipping mobile tel-national webauthn" | "shipping work email" | "shipping work email webauthn" | "shipping work tel" | "shipping work tel webauthn" | "shipping work tel-area-code" | "shipping work tel-area-code webauthn" | "shipping work tel-country-code" | "shipping work tel-country-code webauthn" | "shipping work tel-extension" | "shipping work tel-extension webauthn" | "shipping work tel-local" | "shipping work tel-local webauthn" | "shipping work tel-local-prefix" | "shipping work tel-local-prefix webauthn" | "shipping work tel-local-suffix" | "shipping work tel-local-suffix webauthn" | "shipping work tel-national" | "shipping work tel-national webauthn" | `section-${string} name` | `section-${string} name webauthn` | `section-${string} additional-name` | `section-${string} additional-name webauthn` | `section-${string} address-level1` | `section-${string} address-level1 webauthn` | `section-${string} address-level2` | `section-${string} address-level2 webauthn` | `section-${string} address-level3` | `section-${string} address-level3 webauthn` | `section-${string} address-level4` | `section-${string} address-level4 webauthn` | `section-${string} address-line1` | `section-${string} address-line1 webauthn` | `section-${string} address-line2` | `section-${string} address-line2 webauthn` | `section-${string} address-line3` | `section-${string} address-line3 webauthn` | `section-${string} bday-day` | `section-${string} bday-day webauthn` | `section-${string} bday-month` | `section-${string} bday-month webauthn` | `section-${string} bday-year` | `section-${string} bday-year webauthn` | `section-${string} cc-csc` | `section-${string} cc-csc webauthn` | `section-${string} cc-exp` | `section-${string} cc-exp webauthn` | `section-${string} cc-exp-month` | `section-${string} cc-exp-month webauthn` | `section-${string} cc-exp-year` | `section-${string} cc-exp-year webauthn` | `section-${string} cc-family-name` | `section-${string} cc-family-name webauthn` | `section-${string} cc-given-name` | `section-${string} cc-given-name webauthn` | `section-${string} cc-name` | `section-${string} cc-name webauthn` | `section-${string} cc-number` | `section-${string} cc-number webauthn` | `section-${string} cc-type` | `section-${string} cc-type webauthn` | `section-${string} country` | `section-${string} country webauthn` | `section-${string} country-name` | `section-${string} country-name webauthn` | `section-${string} current-password` | `section-${string} current-password webauthn` | `section-${string} family-name` | `section-${string} family-name webauthn` | `section-${string} given-name` | `section-${string} given-name webauthn` | `section-${string} honorific-prefix` | `section-${string} honorific-prefix webauthn` | `section-${string} honorific-suffix` | `section-${string} honorific-suffix webauthn` | `section-${string} new-password` | `section-${string} new-password webauthn` | `section-${string} one-time-code` | `section-${string} one-time-code webauthn` | `section-${string} organization` | `section-${string} organization webauthn` | `section-${string} postal-code` | `section-${string} postal-code webauthn` | `section-${string} street-address` | `section-${string} street-address webauthn` | `section-${string} transaction-amount` | `section-${string} transaction-amount webauthn` | `section-${string} transaction-currency` | `section-${string} transaction-currency webauthn` | `section-${string} username` | `section-${string} username webauthn` | `section-${string} email` | `section-${string} email webauthn` | `section-${string} tel` | `section-${string} tel webauthn` | `section-${string} tel-area-code` | `section-${string} tel-area-code webauthn` | `section-${string} tel-country-code` | `section-${string} tel-country-code webauthn` | `section-${string} tel-extension` | `section-${string} tel-extension webauthn` | `section-${string} tel-local` | `section-${string} tel-local webauthn` | `section-${string} tel-local-prefix` | `section-${string} tel-local-prefix webauthn` | `section-${string} tel-local-suffix` | `section-${string} tel-local-suffix webauthn` | `section-${string} tel-national` | `section-${string} tel-national webauthn` | `section-${string} home email` | `section-${string} home email webauthn` | `section-${string} home tel` | `section-${string} home tel webauthn` | `section-${string} home tel-area-code` | `section-${string} home tel-area-code webauthn` | `section-${string} home tel-country-code` | `section-${string} home tel-country-code webauthn` | `section-${string} home tel-extension` | `section-${string} home tel-extension webauthn` | `section-${string} home tel-local` | `section-${string} home tel-local webauthn` | `section-${string} home tel-local-prefix` | `section-${string} home tel-local-prefix webauthn` | `section-${string} home tel-local-suffix` | `section-${string} home tel-local-suffix webauthn` | `section-${string} home tel-national` | `section-${string} home tel-national webauthn` | `section-${string} mobile email` | `section-${string} mobile email webauthn` | `section-${string} mobile tel` | `section-${string} mobile tel webauthn` | `section-${string} mobile tel-area-code` | `section-${string} mobile tel-area-code webauthn` | `section-${string} mobile tel-country-code` | `section-${string} mobile tel-country-code webauthn` | `section-${string} mobile tel-extension` | `section-${string} mobile tel-extension webauthn` | `section-${string} mobile tel-local` | `section-${string} mobile tel-local webauthn` | `section-${string} mobile tel-local-prefix` | `section-${string} mobile tel-local-prefix webauthn` | `section-${string} mobile tel-local-suffix` | `section-${string} mobile tel-local-suffix webauthn` | `section-${string} mobile tel-national` | `section-${string} mobile tel-national webauthn` | `section-${string} work email` | `section-${string} work email webauthn` | `section-${string} work tel` | `section-${string} work tel webauthn` | `section-${string} work tel-area-code` | `section-${string} work tel-area-code webauthn` | `section-${string} work tel-country-code` | `section-${string} work tel-country-code webauthn` | `section-${string} work tel-extension` | `section-${string} work tel-extension webauthn` | `section-${string} work tel-local` | `section-${string} work tel-local webauthn` | `section-${string} work tel-local-prefix` | `section-${string} work tel-local-prefix webauthn` | `section-${string} work tel-local-suffix` | `section-${string} work tel-local-suffix webauthn` | `section-${string} work tel-national` | `section-${string} work tel-national webauthn` | `section-${string} billing name` | `section-${string} billing name webauthn` | `section-${string} billing additional-name` | `section-${string} billing additional-name webauthn` | `section-${string} billing address-level1` | `section-${string} billing address-level1 webauthn` | `section-${string} billing address-level2` | `section-${string} billing address-level2 webauthn` | `section-${string} billing address-level3` | `section-${string} billing address-level3 webauthn` | `section-${string} billing address-level4` | `section-${string} billing address-level4 webauthn` | `section-${string} billing address-line1` | `section-${string} billing address-line1 webauthn` | `section-${string} billing address-line2` | `section-${string} billing address-line2 webauthn` | `section-${string} billing address-line3` | `section-${string} billing address-line3 webauthn` | `section-${string} billing bday-day` | `section-${string} billing bday-day webauthn` | `section-${string} billing bday-month` | `section-${string} billing bday-month webauthn` | `section-${string} billing bday-year` | `section-${string} billing bday-year webauthn` | `section-${string} billing cc-csc` | `section-${string} billing cc-csc webauthn` | `section-${string} billing cc-exp` | `section-${string} billing cc-exp webauthn` | `section-${string} billing cc-exp-month` | `section-${string} billing cc-exp-month webauthn` | `section-${string} billing cc-exp-year` | `section-${string} billing cc-exp-year webauthn` | `section-${string} billing cc-family-name` | `section-${string} billing cc-family-name webauthn` | `section-${string} billing cc-given-name` | `section-${string} billing cc-given-name webauthn` | `section-${string} billing cc-name` | `section-${string} billing cc-name webauthn` | `section-${string} billing cc-number` | `section-${string} billing cc-number webauthn` | `section-${string} billing cc-type` | `section-${string} billing cc-type webauthn` | `section-${string} billing country` | `section-${string} billing country webauthn` | `section-${string} billing country-name` | `section-${string} billing country-name webauthn` | `section-${string} billing current-password` | `section-${string} billing current-password webauthn` | `section-${string} billing family-name` | `section-${string} billing family-name webauthn` | `section-${string} billing given-name` | `section-${string} billing given-name webauthn` | `section-${string} billing honorific-prefix` | `section-${string} billing honorific-prefix webauthn` | `section-${string} billing honorific-suffix` | `section-${string} billing honorific-suffix webauthn` | `section-${string} billing new-password` | `section-${string} billing new-password webauthn` | `section-${string} billing one-time-code` | `section-${string} billing one-time-code webauthn` | `section-${string} billing organization` | `section-${string} billing organization webauthn` | `section-${string} billing postal-code` | `section-${string} billing postal-code webauthn` | `section-${string} billing street-address` | `section-${string} billing street-address webauthn` | `section-${string} billing transaction-amount` | `section-${string} billing transaction-amount webauthn` | `section-${string} billing transaction-currency` | `section-${string} billing transaction-currency webauthn` | `section-${string} billing username` | `section-${string} billing username webauthn` | `section-${string} billing email` | `section-${string} billing email webauthn` | `section-${string} billing tel` | `section-${string} billing tel webauthn` | `section-${string} billing tel-area-code` | `section-${string} billing tel-area-code webauthn` | `section-${string} billing tel-country-code` | `section-${string} billing tel-country-code webauthn` | `section-${string} billing tel-extension` | `section-${string} billing tel-extension webauthn` | `section-${string} billing tel-local` | `section-${string} billing tel-local webauthn` | `section-${string} billing tel-local-prefix` | `section-${string} billing tel-local-prefix webauthn` | `section-${string} billing tel-local-suffix` | `section-${string} billing tel-local-suffix webauthn` | `section-${string} billing tel-national` | `section-${string} billing tel-national webauthn` | `section-${string} billing home email` | `section-${string} billing home email webauthn` | `section-${string} billing home tel` | `section-${string} billing home tel webauthn` | `section-${string} billing home tel-area-code` | `section-${string} billing home tel-area-code webauthn` | `section-${string} billing home tel-country-code` | `section-${string} billing home tel-country-code webauthn` | `section-${string} billing home tel-extension` | `section-${string} billing home tel-extension webauthn` | `section-${string} billing home tel-local` | `section-${string} billing home tel-local webauthn` | `section-${string} billing home tel-local-prefix` | `section-${string} billing home tel-local-prefix webauthn` | `section-${string} billing home tel-local-suffix` | `section-${string} billing home tel-local-suffix webauthn` | `section-${string} billing home tel-national` | `section-${string} billing home tel-national webauthn` | `section-${string} billing mobile email` | `section-${string} billing mobile email webauthn` | `section-${string} billing mobile tel` | `section-${string} billing mobile tel webauthn` | `section-${string} billing mobile tel-area-code` | `section-${string} billing mobile tel-area-code webauthn` | `section-${string} billing mobile tel-country-code` | `section-${string} billing mobile tel-country-code webauthn` | `section-${string} billing mobile tel-extension` | `section-${string} billing mobile tel-extension webauthn` | `section-${string} billing mobile tel-local` | `section-${string} billing mobile tel-local webauthn` | `section-${string} billing mobile tel-local-prefix` | `section-${string} billing mobile tel-local-prefix webauthn` | `section-${string} billing mobile tel-local-suffix` | `section-${string} billing mobile tel-local-suffix webauthn` | `section-${string} billing mobile tel-national` | `section-${string} billing mobile tel-national webauthn` | `section-${string} billing work email` | `section-${string} billing work email webauthn` | `section-${string} billing work tel` | `section-${string} billing work tel webauthn` | `section-${string} billing work tel-area-code` | `section-${string} billing work tel-area-code webauthn` | `section-${string} billing work tel-country-code` | `section-${string} billing work tel-country-code webauthn` | `section-${string} billing work tel-extension` | `section-${string} billing work tel-extension webauthn` | `section-${string} billing work tel-local` | `section-${string} billing work tel-local webauthn` | `section-${string} billing work tel-local-prefix` | `section-${string} billing work tel-local-prefix webauthn` | `section-${string} billing work tel-local-suffix` | `section-${string} billing work tel-local-suffix webauthn` | `section-${string} billing work tel-national` | `section-${string} billing work tel-national webauthn` | `section-${string} shipping name` | `section-${string} shipping name webauthn` | `section-${string} shipping additional-name` | `section-${string} shipping additional-name webauthn` | `section-${string} shipping address-level1` | `section-${string} shipping address-level1 webauthn` | `section-${string} shipping address-level2` | `section-${string} shipping address-level2 webauthn` | `section-${string} shipping address-level3` | `section-${string} shipping address-level3 webauthn` | `section-${string} shipping address-level4` | `section-${string} shipping address-level4 webauthn` | `section-${string} shipping address-line1` | `section-${string} shipping address-line1 webauthn` | `section-${string} shipping address-line2` | `section-${string} shipping address-line2 webauthn` | `section-${string} shipping address-line3` | `section-${string} shipping address-line3 webauthn` | `section-${string} shipping bday-day` | `section-${string} shipping bday-day webauthn` | `section-${string} shipping bday-month` | `section-${string} shipping bday-month webauthn` | `section-${string} shipping bday-year` | `section-${string} shipping bday-year webauthn` | `section-${string} shipping cc-csc` | `section-${string} shipping cc-csc webauthn` | `section-${string} shipping cc-exp` | `section-${string} shipping cc-exp webauthn` | `section-${string} shipping cc-exp-month` | `section-${string} shipping cc-exp-month webauthn` | `section-${string} shipping cc-exp-year` | `section-${string} shipping cc-exp-year webauthn` | `section-${string} shipping cc-family-name` | `section-${string} shipping cc-family-name webauthn` | `section-${string} shipping cc-given-name` | `section-${string} shipping cc-given-name webauthn` | `section-${string} shipping cc-name` | `section-${string} shipping cc-name webauthn` | `section-${string} shipping cc-number` | `section-${string} shipping cc-number webauthn` | `section-${string} shipping cc-type` | `section-${string} shipping cc-type webauthn` | `section-${string} shipping country` | `section-${string} shipping country webauthn` | `section-${string} shipping country-name` | `section-${string} shipping country-name webauthn` | `section-${string} shipping current-password` | `section-${string} shipping current-password webauthn` | `section-${string} shipping family-name` | `section-${string} shipping family-name webauthn` | `section-${string} shipping given-name` | `section-${string} shipping given-name webauthn` | `section-${string} shipping honorific-prefix` | `section-${string} shipping honorific-prefix webauthn` | `section-${string} shipping honorific-suffix` | `section-${string} shipping honorific-suffix webauthn` | `section-${string} shipping new-password` | `section-${string} shipping new-password webauthn` | `section-${string} shipping one-time-code` | `section-${string} shipping one-time-code webauthn` | `section-${string} shipping organization` | `section-${string} shipping organization webauthn` | `section-${string} shipping postal-code` | `section-${string} shipping postal-code webauthn` | `section-${string} shipping street-address` | `section-${string} shipping street-address webauthn` | `section-${string} shipping transaction-amount` | `section-${string} shipping transaction-amount webauthn` | `section-${string} shipping transaction-currency` | `section-${string} shipping transaction-currency webauthn` | `section-${string} shipping username` | `section-${string} shipping username webauthn` | `section-${string} shipping email` | `section-${string} shipping email webauthn` | `section-${string} shipping tel` | `section-${string} shipping tel webauthn` | `section-${string} shipping tel-area-code` | `section-${string} shipping tel-area-code webauthn` | `section-${string} shipping tel-country-code` | `section-${string} shipping tel-country-code webauthn` | `section-${string} shipping tel-extension` | `section-${string} shipping tel-extension webauthn` | `section-${string} shipping tel-local` | `section-${string} shipping tel-local webauthn` | `section-${string} shipping tel-local-prefix` | `section-${string} shipping tel-local-prefix webauthn` | `section-${string} shipping tel-local-suffix` | `section-${string} shipping tel-local-suffix webauthn` | `section-${string} shipping tel-national` | `section-${string} shipping tel-national webauthn` | `section-${string} shipping home email` | `section-${string} shipping home email webauthn` | `section-${string} shipping home tel` | `section-${string} shipping home tel webauthn` | `section-${string} shipping home tel-area-code` | `section-${string} shipping home tel-area-code webauthn` | `section-${string} shipping home tel-country-code` | `section-${string} shipping home tel-country-code webauthn` | `section-${string} shipping home tel-extension` | `section-${string} shipping home tel-extension webauthn` | `section-${string} shipping home tel-local` | `section-${string} shipping home tel-local webauthn` | `section-${string} shipping home tel-local-prefix` | `section-${string} shipping home tel-local-prefix webauthn` | `section-${string} shipping home tel-local-suffix` | `section-${string} shipping home tel-local-suffix webauthn` | `section-${string} shipping home tel-national` | `section-${string} shipping home tel-national webauthn` | `section-${string} shipping mobile email` | `section-${string} shipping mobile email webauthn` | `section-${string} shipping mobile tel` | `section-${string} shipping mobile tel webauthn` | `section-${string} shipping mobile tel-area-code` | `section-${string} shipping mobile tel-area-code webauthn` | `section-${string} shipping mobile tel-country-code` | `section-${string} shipping mobile tel-country-code webauthn` | `section-${string} shipping mobile tel-extension` | `section-${string} shipping mobile tel-extension webauthn` | `section-${string} shipping mobile tel-local` | `section-${string} shipping mobile tel-local webauthn` | `section-${string} shipping mobile tel-local-prefix` | `section-${string} shipping mobile tel-local-prefix webauthn` | `section-${string} shipping mobile tel-local-suffix` | `section-${string} shipping mobile tel-local-suffix webauthn` | `section-${string} shipping mobile tel-national` | `section-${string} shipping mobile tel-national webauthn` | `section-${string} shipping work email` | `section-${string} shipping work email webauthn` | `section-${string} shipping work tel` | `section-${string} shipping work tel webauthn` | `section-${string} shipping work tel-area-code` | `section-${string} shipping work tel-area-code webauthn` | `section-${string} shipping work tel-country-code` | `section-${string} shipping work tel-country-code webauthn` | `section-${string} shipping work tel-extension` | `section-${string} shipping work tel-extension webauthn` | `section-${string} shipping work tel-local` | `section-${string} shipping work tel-local webauthn` | `section-${string} shipping work tel-local-prefix` | `section-${string} shipping work tel-local-prefix webauthn` | `section-${string} shipping work tel-local-suffix` | `section-${string} shipping work tel-local-suffix webauthn` | `section-${string} shipping work tel-national` | `section-${string} shipping work tel-national webauthn`) | (() => AutoFill))[], unknown, unknown, "off", boolean>;
    formatter: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    parser: {
        readonly type: import("vue").PropType<Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    placeholder: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    form: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly: BooleanConstructor;
    clearable: BooleanConstructor;
    clearIcon: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component) | ((new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    showPassword: BooleanConstructor;
    showWordLimit: BooleanConstructor;
    wordLimitPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "inside" | "outside", unknown, "inside", boolean>;
    suffixIcon: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component) | ((new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    prefixIcon: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component) | ((new (...args: any[]) => (string | import("vue").Component) & {}) | (() => string | import("vue").Component))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    containerRole: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, undefined, boolean>;
    tabindex: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, 0, boolean>;
    validateEvent: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    inputStyle: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | import("vue").CSSProperties | import("vue").StyleValue[]) | (() => import("vue").StyleValue) | ((new (...args: any[]) => string | import("vue").CSSProperties | import("vue").StyleValue[]) | (() => import("vue").StyleValue))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    autofocus: BooleanConstructor;
    rows: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 2, boolean>;
};
export declare const mentionEmits: {
    "update:modelValue": (value: string) => boolean;
    'whole-remove': (pattern: string, prefix: string) => boolean;
    input: (value: string) => boolean;
    search: (pattern: string, prefix: string) => boolean;
    select: (option: MentionOption, prefix: string) => boolean;
    focus: (evt: FocusEvent) => boolean;
    blur: (evt: FocusEvent) => boolean;
};
export type MentionEmits = typeof mentionEmits;
export type MentionProps = ExtractPropTypes<typeof mentionProps>;
export type MentionPropsPublic = __ExtractPublicPropTypes<typeof mentionProps>;
export type MentionInstance = InstanceType<typeof Mention> & unknown;
export type { MentionOption } from './types';
export type MentionOptionProps = {
    value?: string;
    label?: string;
    disabled?: string;
    [key: string]: string | undefined;
};
export declare const mentionDefaultProps: Required<MentionOptionProps>;
