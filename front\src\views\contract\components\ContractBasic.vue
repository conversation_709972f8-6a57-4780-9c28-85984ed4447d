<template>
  <div class="contract-basic">
    <el-form ref="contractBasicRef" :model="form" :rules="rules" label-width="120px">
      <!-- 基本信息 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">基本信息</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同名称" prop="contractName">
            <el-input v-model="form.contractName" placeholder="请输入合同名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNumber">
            <el-input v-model="form.contractNumber" placeholder="请输入合同编号" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="外部合同编号" prop="externalContractNumber">
            <el-input v-model="form.externalContractNumber" placeholder="请输入外部合同编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
              <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="项目所在地区" prop="regionArea">
            <el-cascader
              v-model="regionValue"
              :options="regionOptions"
              placeholder="请选择项目所在地区"
              style="width: 44%"
              clearable
              filterable
              @change="handleRegionChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="委托单位" prop="clientName">
            <el-autocomplete
              v-model="form.clientName"
              :fetch-suggestions="queryCustomers"
              clearable
              placeholder="请选择或输入委托单位"
              @select="handleClientChange"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="委托单位联系人" prop="clientContact">
            <el-select
              v-model="form.clientContact"
              placeholder="请选择或输入联系人"
              style="width: 100%"
              filterable
              clearable
              allow-create
              default-first-option
            >
              <el-option
                v-for="contact in currentCustomerContacts"
                :key="contact"
                :label="contact"
                :value="contact"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="取得方式" prop="acquisitionMethod">
            <el-select v-model="form.acquisitionMethod" placeholder="请选择取得方式" style="width: 100%">
              <el-option label="商务谈判" value="商务谈判" />
              <el-option label="公开招标" value="公开招标" />
              <el-option label="邀标" value="邀标" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="deptId">
            <DeptTreeSelect v-model="form.deptId" placeholder="请选择所属部门" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目负责人" prop="projectManagerIds">
            <el-select
              v-model="form.projectManagerIds"
              multiple
              placeholder="请选择项目负责人"
              style="width: 100%"
              filterable
              remote
              remote-show-suffix
              :remote-method="searchUsers"
              :loading="userLoading"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目客服" prop="projectServiceId">
            <el-select
              v-model="form.projectServiceId"
              placeholder="请选择项目客服"
              style="width: 100%"
              filterable
              remote
              remote-show-suffix
              :remote-method="searchUsers"
              :loading="userLoading"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 金额信息 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">金额信息</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同签订日期" prop="contractSignDate">
            <el-date-picker
              v-model="form.contractSignDate"
              type="date"
              placeholder="选择合同签订日期"
              style="width: 100%"
              :default-value="new Date()"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额类型" prop="amountType">
            <el-select v-model="form.amountType" placeholder="请选择金额类型" style="width: 100%">
              <el-option label="固定金额" value="固定金额" />
              <el-option label="非固定金额" value="非固定金额" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同金额" prop="contractAmount">
            <el-input-number
              v-model="form.contractAmount"
              :precision="2"
              :min="0"
              placeholder="合同金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报价单总金额" prop="quotationTotalAmount">
            <el-input-number
              v-model="form.quotationTotalAmount"
              :precision="2"
              :min="0"
              placeholder="报价单总金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="变更后合同金额" prop="changedContractAmount">
            <el-input-number
              v-model="form.changedContractAmount"
              :precision="2"
              :min="0"
              placeholder="变更后合同金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="外协金额" prop="outsourcingAmount">
            <el-input-number
              v-model="form.outsourcingAmount"
              :precision="2"
              :min="0"
              placeholder="外协金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他信息 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">其他信息</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="外协单位" prop="outsourcingCompany">
            <el-input v-model="form.outsourcingCompany" placeholder="请输入外协单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完工时间" prop="completionTime">
            <el-date-picker
              v-model="form.completionTime"
              type="date"
              placeholder="选择完工时间"
              style="width: 100%"
              :default-value="new Date()"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同状态" prop="contractStatus">
            <el-select v-model="form.contractStatus" placeholder="请选择合同状态" style="width: 100%">
              <el-option
                v-for="item in contractStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 附件上传 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">合同附件</span>
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件上传">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :data="uploadData"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="deleteContractAttachment"
              :before-upload="beforeUpload"
              :file-list="fileList"
              multiple
              :limit="10"
              :on-exceed="handleExceed"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传多个文件，单个文件大小不超过50MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 附件列表 -->
      <el-row :gutter="20" v-if="attachmentList.length > 0">
        <el-col :span="24">
          <el-form-item label="已上传附件">
            <el-table :data="attachmentList" style="width: 100%" size="small">
              <el-table-column prop="originalFilename" label="文件名" min-width="200" />
              <el-table-column prop="fileSizeMb" label="文件大小" width="100">
                <template #default="scope">
                  {{ scope.row.fileSizeMb }}MB
                </template>
              </el-table-column>
              <el-table-column prop="uploadTime" label="上传时间" width="150" />
              <el-table-column prop="createBy" label="上传人" width="100" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="downloadContractAttachment(scope.row)">
                    下载
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteContractAttachment(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 保存按钮 -->
    <div style="text-align: center; margin-top: 20px;">
      <el-button type="primary" @click="saveBasicInfo" :loading="saving">保存基本信息</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { getUserList, addContract, updateContract, calculateQuotationTotalAmount } from "@/api/contract/contract"
import { searchCustomer } from "@/api/quotation/projectQuotation"
import { getAttachmentList, deleteAttachment, downloadAttachment } from "@/api/contract/attachment"
import DeptTreeSelect from '@/components/DeptTreeSelect/index.vue'
import { provinceAndCityData } from 'element-china-area-data'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 表单引用
const contractBasicRef = ref(null)

// 保存状态
const saving = ref(false)

// 附件相关
const uploadRef = ref(null)
const fileList = ref([])
const attachmentList = ref([])
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/contract/attachment/upload')
const uploadHeaders = ref({
  Authorization: 'Bearer ' + getToken()
})
const uploadData = ref({
  contract_id: null,
  remark: ''
})

// 表单数据
const form = ref({
  contractName: '',
  contractNumber: '',
  externalContractNumber: '',
  businessType: '',
  regionProvince: '',
  regionCity: '',
  clientName: '',
  clientContact: '',
  acquisitionMethod: '',
  projectManagerIds: [],
  projectServiceId: null,
  deptId: null,
  amountType: '',
  contractAmount: null,
  quotationTotalAmount: null,
  changedContractAmount: null,
  outsourcingAmount: null,
  outsourcingCompany: '',
  completionTime: '',
  contractSignDate: '',
  contractStatus: '',
  remark: ''
})

// 用户选择相关
const userOptions = ref([])
const userLoading = ref(false)

// 客户搜索相关
const customerOptions = ref([])
const customerLoading = ref(false)
const currentCustomerContacts = ref([])

// 地址选择器数据和配置
const regionOptions = ref(provinceAndCityData)
const regionValue = ref([])

// 业务类型选项
const businessTypeOptions = ref([
  { label: "一般委托", value: "一般委托" },
  { label: "年度", value: "年度" },
  { label: "验收", value: "验收" },
  { label: "送样", value: "送样" },
  { label: "监督监测", value: "监督监测" },
  { label: "应急监测", value: "应急监测" },
  { label: "环评检测", value: "环评检测" },
  { label: "场调检测", value: "场调检测" },
  { label: "地下水专项", value: "地下水专项" },
  { label: "生态调查", value: "生态调查" },
  { label: "辐射", value: "辐射" },
  { label: "油气回收", value: "油气回收" },
  { label: "LADR", value: "LADR" },
  { label: "咨询服务", value: "咨询服务" },
  { label: "数字化服务", value: "数字化服务" },
  { label: "监理", value: "监理" },
  { label: "其他技术服务", value: "其他技术服务" },
  { label: "其他", value: "其他" },
  { label: "预留1", value: "预留1" },
  { label: "预留2", value: "预留2" },
  { label: "预留3", value: "预留3" }
])

// 合同状态选项
const contractStatusOptions = ref([
  { label: "草稿", value: "草稿" },
  { label: "待审核", value: "待审核" },
  { label: "已审核", value: "已审核" },
  { label: "执行中", value: "执行中" },
  { label: "已完成", value: "已完成" },
  { label: "已取消", value: "已取消" }
])

// 表单验证规则
const rules = {
  contractName: [
    { required: true, message: "合同名称不能为空", trigger: "blur" }
  ],
  contractNumber: [
    { required: true, message: "合同编号不能为空", trigger: "blur" }
  ],
  businessType: [
    { required: true, message: "业务类型不能为空", trigger: "change" }
  ],
  clientName: [
    { required: true, message: "委托单位不能为空", trigger: "blur" }
  ],
  clientContact: [
    { required: true, message: "委托单位联系人不能为空", trigger: "blur" }
  ],
  acquisitionMethod: [
    { required: true, message: "取得方式不能为空", trigger: "change" }
  ],
  projectManagerIds: [
    { required: true, message: "项目负责人不能为空", trigger: "change" }
  ],
  projectServiceId: [
    { required: true, message: "项目客服不能为空", trigger: "change" }
  ],
  amountType: [
    { required: true, message: "金额类型不能为空", trigger: "change" }
  ],
  contractAmount: [
    { required: true, message: "合同金额不能为空", trigger: "blur" }
  ],
  contractSignDate: [
    { required: true, message: "合同签订日期不能为空", trigger: "blur" }
  ],
  completionTime: [
    { required: true, message: "完工时间不能为空", trigger: "blur" }
  ]
}

// 搜索用户
const searchUsers = async (query) => {
  if (query) {
    userLoading.value = true
    try {
      const response = await getUserList({ nickName: query })
      userOptions.value = response.rows || []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      userOptions.value = []
    } finally {
      userLoading.value = false
    }
  } else {
    // 如果没有查询条件，显示初始用户列表
    await initUserList()
  }
}

// 初始化用户列表
const initUserList = async () => {
  try {
    const response = await getUserList({ pageSize: 100 })
    console.info('初始化用户列表', response.rows?.length || 0)
    userOptions.value = response.rows || []
  } catch (error) {
    console.error('初始化用户列表失败:', error)
  }
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }

  // 使用正确的API接口
  searchCustomer({ keyword: queryString }).then(response => {
    const customers = response.data.map(item => {
      return { value: item.name, id: item.id, data: item }
    })
    // console.log("customers", customers)
    cb(customers)
  }).catch(error => {
    console.error('查询客户失败:', error)
    cb([])
  })
}

// 处理客户变化
const handleClientChange = (item) => {
  // 根据选择的客户获取联系人列表
  console.log("handleClientChange:", item)
  // 清空联系人选择
  if (item.data) {
    form.value.clientContact = item.data.contactName
  }
}

// 处理地区选择变化
const handleRegionChange = (val) => {
  if (val && val.length > 0) {
    // 根据选择的地区代码获取地区名称
    const province = regionOptions.value.find(item => item.value === val[0])
    form.value.regionProvince = province?.label || ''

    if (val.length > 1) {
      const city = province?.children?.find(item => item.value === val[1])
      form.value.regionCity = city?.label || ''
    } else {
      form.value.regionCity = ''
    }
  } else {
    form.value.regionProvince = ''
    form.value.regionCity = ''
  }
  console.log("form.value.projectProvince", form.projectProvince)
}

// 监听表单数据变化
watch(form, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  console.log("ContractBasic: modelValue changed", newVal)
  if (newVal && Object.keys(newVal).length > 0) {
    // 使用nextTick确保DOM更新后再设置数据
    nextTick(() => {
      Object.assign(form.value, newVal)

      // 设置地区选择器的值
      if (newVal.regionProvince && newVal.regionCity) {
        const province = regionOptions.value.find(item => item.label === newVal.regionProvince)
        if (province) {
          const city = province.children?.find(item => item.label === newVal.regionCity)
          if (city) {
            regionValue.value = [province.value, city.value]
          } else {
            regionValue.value = [province.value]
          }
        }
      }

      // 如果有客户名称，触发联系人加载
      if (newVal.clientName) {
        handleClientChange(newVal.clientName)
      }
    })
  }
}, { immediate: true, deep: true })

// 验证表单
const validate = () => {
  return contractBasicRef.value.validate()
}

// 重置表单
const resetForm = () => {
  contractBasicRef.value.resetFields()
}

// 附件相关方法
const loadAttachmentList = async () => {
  if (!form.value.id) return

  try {
    const response = await getAttachmentList({
      contract_id: form.value.id,
      pageNum: 1,
      pageSize: 100
    })
    if (response.code === 200) {
      attachmentList.value = response.data.attachments || []
    }
  } catch (error) {
    console.error('加载附件列表失败:', error)
  }
}

const beforeUpload = (file) => {
  if (!form.value.id) {
    ElMessage.warning('请先保存合同基本信息后再上传附件')
    return false
  }

  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过 50MB!')
    return false
  }

  // 更新上传参数
  uploadData.value.contract_id = form.value.id
  return true
}

const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    ElMessage.success('文件上传成功')
    loadAttachmentList() // 重新加载附件列表
  } else {
    ElMessage.error(response.msg || '文件上传失败')
  }
}

const handleUploadError = (error, file) => {
  ElMessage.error('文件上传失败: ' + error.message)
}

const handleRemove = (file) => {
  // 文件移除处理
  // 调用删除接口
  proxy.$modal.confirm('确定要删除该附件吗？').then(() => {
    // 这里可以调用删除文件的API
    let file_path = form.value.attachments[index].filePath;
    delAttachment(file_path).then(response => {
        form.value.attachments.splice(index, 1)
        proxy.$modal.msgSuccess("附件删除成功")
    })
  })
}

const handleExceed = (files, fileList) => {
  ElMessage.warning('最多只能上传10个文件')
}

const downloadContractAttachment = async (attachment) => {
  try {
    const response = await downloadAttachment(attachment.id)
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', attachment.originalFilename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('文件下载失败')
  }
}

const deleteContractAttachment = async (attachment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除附件"${attachment.originalFilename}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await deleteAttachment(attachment.id)
    if (response.code === 200) {
      ElMessage.success('附件删除成功')
      loadAttachmentList() // 重新加载附件列表
    } else {
      ElMessage.error(response.msg || '附件删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('附件删除失败')
    }
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm,
  loadAttachmentList
})

// 保存基本信息
const saveBasicInfo = async () => {
  try {
    saving.value = true

    // 验证表单
    await contractBasicRef.value.validate()

    const contractData = { ...form.value }

    if (contractData.id) {
      // 更新合同
      await updateContract(contractData)
      ElMessage.success('合同基本信息更新成功')
    } else {
      // 新增合同
      const result = await addContract(contractData)
      if (result.data && result.data.id) {
        form.value.id = result.data.id
        emit('update:modelValue', form.value)
        // 新增成功后加载附件列表
        await loadAttachmentList()
      }
      ElMessage.success('合同基本信息保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  initUserList()
  // initRegionData()
  // 如果有合同ID，加载附件列表
  if (form.value.id) {
    loadAttachmentList()
  }
})
</script>

<style scoped>
.contract-basic {
  padding: 20px;
}
</style>
