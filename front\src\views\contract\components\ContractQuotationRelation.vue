<template>
  <div class="contract-quotation-relation">
    <!-- 操作按钮 -->
    <div v-if="!readonly" style="margin-bottom: 20px;">
      <el-button type="primary" icon="Plus" @click="handleAdd">新增关联项目报价</el-button>
      <el-button
        type="danger"
        icon="Delete"
        :disabled="selectedRelations.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tempRelationList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      empty-text="暂无关联的项目报价"
    >
      <el-table-column v-if="!readonly" type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" min-width="120" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.projectCode }}</span>
          <el-tag v-if="scope.row.isNew" type="success" size="small" style="margin-left: 5px;">新增</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" show-overflow-tooltip />
      <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" show-overflow-tooltip />
      <el-table-column label="项目负责人" align="center" prop="projectManager" min-width="100" show-overflow-tooltip />
      <el-table-column label="项目状态" align="center" prop="status" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status">{{ getStatusType(scope.row.status) }}</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150" />
    </el-table>
    
    <!-- 保存按钮 -->
    <div v-if="!readonly" style="text-align: center; margin-top: 20px;">
      <el-button
        type="success"
        icon="Check"
        :disabled="!hasChanges"
        @click="handleSaveAll"
        :loading="saving"
        style="margin-left: 10px;"
      >
        保存所有
      </el-button>
    </div>
      
    <!-- 项目报价选择弹框 -->
    <ProjectQuotationSelector
      v-model="selectorVisible"
      :exclude-project-codes="excludeProjectCodes"
      @confirm="handleSelectorConfirm"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ProjectQuotationSelector from '@/components/ProjectQuotationSelector/index.vue'
import {
  listContractQuotationRelation,
  updateContractQuotationRelation
} from '@/api/contract/contractQuotationRelation'

const props = defineProps({
  contractId: {
    type: Number,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 数据
const loading = ref(false)
const saving = ref(false)
const relationList = ref([]) // 从后端加载的原始数据
const tempRelationList = ref([]) // 临时的关联列表（包含新增和删除操作）
const selectedRelations = ref([])
const selectorVisible = ref(false)
const hasChanges = ref(false) // 是否有未保存的变更

// 计算已关联的项目编号，用于排除
const excludeProjectCodes = computed(() => {
  return tempRelationList.value.map(item => item.projectCode)
})

/** 查询关联报价单列表 */
const getList = async () => {
  if (!props.contractId) return

  loading.value = true
  try {
    const response = await listContractQuotationRelation(props.contractId)
    if (response.code === 200) {
      relationList.value = response.data.rows || []
      // 同时更新临时列表
      tempRelationList.value = [...relationList.value]
      hasChanges.value = false
    }
  } catch (error) {
    console.error('查询合同关联报价单失败:', error)
    ElMessage.error('查询关联报价单失败')
  } finally {
    loading.value = false
  }
}

// 监听合同ID变化
watch(() => props.contractId, (newVal) => {
  if (newVal) {
    getList()
  }
}, { immediate: true })



/** 新增关联项目报价 */
const handleAdd = () => {
  selectorVisible.value = true
}

/** 选择器确认 */
const handleSelectorConfirm = (selectedProjects) => {
  // 将新选择的项目添加到临时列表中
  selectedProjects.forEach(project => {
    // 检查是否已存在
    const exists = tempRelationList.value.some(item => item.projectCode === project.projectCode)
    if (!exists) {
      tempRelationList.value.push({
        projectCode: project.projectCode,
        projectName: project.projectName,
        customerName: project.customerName,
        projectManager: project.projectManager,
        status: project.status,
        createTime: new Date().toISOString().split('T')[0],
        isNew: true // 标记为新增项
      })
    }
  })

  hasChanges.value = true
  ElMessage.success('已添加到待保存列表')
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  selectedRelations.value = selection
}


/** 批量删除 */
const handleBatchDelete = async () => {
  if (selectedRelations.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedRelations.value.length} 个关联项目报价吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 获取要删除的项目编号
    const deleteProjectCodes = selectedRelations.value.map(item => item.projectCode)

    // 从临时列表中移除选中的项目
    tempRelationList.value = tempRelationList.value.filter(item =>
      !deleteProjectCodes.includes(item.projectCode)
    )

    hasChanges.value = true
    selectedRelations.value = []
    ElMessage.success('已从待保存列表中批量删除')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除关联项目报价失败:', error)
    }
  }
}

/** 保存所有项目报价 */
const handleSaveAll = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有需要保存的变更')
    return
  }

  saving.value = true
  try {
    // 获取所有项目编号
    const allProjectCodes = tempRelationList.value.map(item => item.projectCode)

    const data = {
      projectCodes: allProjectCodes
    }

    await updateContractQuotationRelation(props.contractId, data)
    ElMessage.success('保存所有项目报价成功')

    // 重新加载数据
    await getList()
  } catch (error) {
    console.error('保存所有项目报价失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

function getStatusType(status) {
  // 项目审批状态：0-草稿，1-待审核，2-已审核，3-已撤回，4-已拒绝
  switch (status) {
    case '0':
      return '草稿'
    case '1':
      return '待审批'
    case '2':
      return '已审批'
    case '3':
      return '已撤回'
    case '4': 
      return '已拒绝'
    default:
      return '草稿'
  }
}

</script>

<style scoped>
.contract-quotation-relation {
  padding: 0;
}
</style>
