<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>项目报价详情 - {{ quotationForm.projectName }}</span>
        </div>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="检测明细报价" name="quotation">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>费用项1：检测项目费用明细</span>
              </div>
            </template>
            <el-table :data="quotationForm.items" style="width: 100%" border>
              <el-table-column label="序号" type="index" width="50" align="center" />
              <el-table-column label="服务类型" prop="serviceType" width="120" />
              <el-table-column label="检测类别" prop="category" width="120" />
              <el-table-column label="检测参数" prop="parameter" width="120" />
              <el-table-column label="检测方法" prop="method" width="120" />
              <el-table-column label="检测编号" prop="testCode" width="120" />
              <el-table-column label="报价编号" prop="priceCode" width="120" />
              <el-table-column label="点位名称" prop="pointName" width="120" />
              <el-table-column label="点位数" prop="pointCount" width="80" />
              <el-table-column label="周期类型" prop="cycleType" width="100" />
              <el-table-column label="周期数" prop="cycleCount" width="80" />
              <el-table-column label="频次数" prop="frequency" width="80" />
              <el-table-column label="样品数" prop="sampleCount" width="80" />
              <el-table-column label="采样单价" prop="samplingPrice" width="100" />
              <el-table-column label="检测单价" prop="testingPrice" width="100" />
              <el-table-column label="差旅费单价" prop="travelPrice" width="100" />
              <el-table-column label="总价" prop="totalPrice" width="100" />
            </el-table>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>费用项2：项目其他费用表</span>
                <el-button v-if="isEditable" type="primary" size="small" @click="addOtherFee">添加其他费用</el-button>
              </div>
            </template>
            <el-table :data="quotationForm.otherFees" style="width: 100%" border>
              <el-table-column label="序号" type="index" width="50" align="center" />
              <el-table-column label="费用名称" prop="feeName" width="150">
                <template #default="scope" v-if="isEditable">
                  <el-input v-model="scope.row.feeName" placeholder="请输入费用名称" />
                </template>
              </el-table-column>
              <el-table-column label="数量" prop="quantity" width="100">
                <template #default="scope" v-if="isEditable">
                  <el-input-number v-model="scope.row.quantity" :min="1" @change="calculateOtherFeeTotal(scope.row)" />
                </template>
              </el-table-column>
              <el-table-column label="单价" prop="unitPrice" width="100">
                <template #default="scope" v-if="isEditable">
                  <el-input-number v-model="scope.row.unitPrice" :precision="2" @change="calculateOtherFeeTotal(scope.row)" />
                </template>
              </el-table-column>
              <el-table-column label="总价" prop="totalPrice" width="100" />
              <el-table-column label="备注" prop="remark">
                <template #default="scope" v-if="isEditable">
                  <el-input v-model="scope.row.remark" placeholder="请输入备注" />
                </template>
              </el-table-column>
              <el-table-column v-if="isEditable" label="操作" width="80">
                <template #default="scope">
                  <el-button type="danger" icon="Delete" circle @click="removeOtherFee(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>费用项3：项目总费用表</span>
              </div>
            </template>
            <el-form :model="totalFee" label-width="180px">
              <el-form-item label="项目编号">
                <el-input v-model="quotationForm.projectCode" disabled />
              </el-form-item>
              <el-form-item label="检测项目总折扣率(%)">
                <el-input-number v-model="totalFee.discountRate" :min="0" :max="100" :precision="2" :disabled="!isEditable" @change="calculateTotalFee" />
              </el-form-item>
              <el-form-item label="检测折后费用">
                <el-input v-model="totalFee.discountedTestingFee" disabled />
              </el-form-item>
              <el-form-item label="其他费用">
                <el-input v-model="totalFee.otherFee" disabled />
              </el-form-item>
              <el-form-item label="优惠前总费用">
                <el-input v-model="totalFee.totalFeeBeforeDiscount" disabled />
              </el-form-item>
              <el-form-item label="税率(%)">
                <el-input-number v-model="totalFee.taxRate" :min="0" :max="100" :precision="2" :disabled="!isEditable" @change="calculateTotalFee" />
              </el-form-item>
              <el-form-item label="税费">
                <el-input v-model="totalFee.tax" disabled />
              </el-form-item>
              <el-form-item label="优惠前总费用(税后)">
                <el-input v-model="totalFee.totalFeeAfterTax" disabled />
              </el-form-item>
              <el-form-item label="整体调整金额">
                <el-input-number v-model="totalFee.adjustmentAmount" :precision="2" :disabled="!isEditable" @change="calculateTotalFee" />
              </el-form-item>
              <el-form-item label="优惠后总金额">
                <el-input v-model="totalFee.finalAmount" disabled />
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <div class="dialog-footer" style="margin-top: 20px; text-align: center;">
        <el-button @click="goBack">返 回</el-button>
        <el-button v-if="isEditable" type="primary" @click="saveQuotation">保 存</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotationFee } from "@/api/quotation/projectQuotation"
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 是否可编辑
const isEditable = ref(false)
// 活动标签页
const activeTab = ref('quotation')
// 报价表单对象
const quotationForm = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  items: [],
  otherFees: [],
  totalFee: null
})
// 总费用
const totalFee = ref({
  discountRate: 95, // 折扣率95%
  discountedTestingFee: 0,
  otherFee: 0,
  totalFeeBeforeDiscount: 0,
  taxRate: 6, // 税率6%
  tax: 0,
  totalFeeAfterTax: 0,
  adjustmentAmount: 0,
  finalAmount: 0
})

// 添加其他费用
const addOtherFee = () => {
  quotationForm.value.otherFees.push({
    feeName: '',
    quantity: 1,
    unitPrice: 0,
    totalPrice: 0,
    remark: ''
  })
}

// 移除其他费用
const removeOtherFee = (index) => {
  quotationForm.value.otherFees.splice(index, 1)
  calculateTotalFee()
}

// 计算其他费用总价
const calculateOtherFeeTotal = (row) => {
  row.totalPrice = (row.quantity || 1) * (row.unitPrice || 0)
  calculateTotalFee()
}

// 计算总费用
const calculateTotalFee = () => {
  // 计算检测项目总费用
  let testingFee = 0
  quotationForm.value.items.forEach(item => {
    testingFee += item.totalPrice || 0
  })

  // 计算其他费用总额
  let otherFeeTotal = 0
  quotationForm.value.otherFees.forEach(fee => {
    otherFeeTotal += fee.totalPrice || 0
  })

  // 计算折后检测费用
  totalFee.value.discountedTestingFee = (testingFee * totalFee.value.discountRate / 100).toFixed(2)

  // 其他费用
  totalFee.value.otherFee = otherFeeTotal.toFixed(2)

  // 优惠前总费用
  totalFee.value.totalFeeBeforeDiscount = (parseFloat(totalFee.value.discountedTestingFee) + parseFloat(totalFee.value.otherFee)).toFixed(2)

  // 税费
  totalFee.value.tax = (totalFee.value.totalFeeBeforeDiscount * totalFee.value.taxRate / 100).toFixed(2)

  // 优惠前总费用(税后)
  totalFee.value.totalFeeAfterTax = (parseFloat(totalFee.value.totalFeeBeforeDiscount) + parseFloat(totalFee.value.tax)).toFixed(2)

  // 优惠后总金额
  totalFee.value.finalAmount = (parseFloat(totalFee.value.totalFeeAfterTax) + parseFloat(totalFee.value.adjustmentAmount || 0)).toFixed(2)
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 保存报价
const saveQuotation = () => {
  // 构建保存对象
  const saveData = {
    id: quotationForm.value.id,
    projectName: quotationForm.value.projectName,
    projectCode: quotationForm.value.projectCode,
    otherFees: quotationForm.value.otherFees,
    totalFee: totalFee.value
  }

  // 调用保存接口
  updateProjectQuotationFee(saveData).then(response => {
    ElMessage.success('保存成功')
    goBack()
  }).catch(error => {
    console.error('保存失败:', error)
  })
}

// 获取项目报价详情
const getQuotationDetail = (id) => {
  getProjectQuotation(id).then(response => {
    quotationForm.value = response.data

    // 如果有总费用数据，则使用后端数据
    if (quotationForm.value.totalFee) {
      totalFee.value = quotationForm.value.totalFee
    } else {
      // 否则计算总费用
      calculateTotalFee()
    }
  })
}

onMounted(() => {
  const id = route.params.id
  if (id) {
    getQuotationDetail(id)
  }

  // 判断是否为编辑模式
  isEditable.value = route.query.edit === 'true'
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-card {
  margin-bottom: 20px;
}
</style>
